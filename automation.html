<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 自动控制</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                <h1>自动控制</h1>
                <p class="page-description">智能控制策略与节能优化管理</p>
            </div>
        </div>

        <!-- 自动控制内容 -->
        <div class="automation-container">
            <div class="container">
                <div class="grid grid-cols-2 gap-6">
                    <!-- 自动控制策略 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-robot"></i>
                                自动控制策略
                            </h3>
                        </div>
                        <div class="strategy-list">
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-name">温湿度自动调节</div>
                                    <div class="strategy-desc">根据设定值自动调节温湿度</div>
                                </div>
                                <div class="strategy-status">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-name">压差联动控制</div>
                                    <div class="strategy-desc">压差异常时自动调节风量</div>
                                </div>
                                <div class="strategy-status">
                                    <label class="switch">
                                        <input type="checkbox" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="strategy-item">
                                <div class="strategy-info">
                                    <div class="strategy-name">节能模式</div>
                                    <div class="strategy-desc">非工作时间自动降低运行参数</div>
                                </div>
                                <div class="strategy-status">
                                    <label class="switch">
                                        <input type="checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 能效优化 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-leaf"></i>
                                能效优化
                            </h3>
                        </div>
                        <div class="efficiency-metrics">
                            <div class="metric-item">
                                <div class="metric-label">当前能效比</div>
                                <div class="metric-value">3.2</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">节能率</div>
                                <div class="metric-value">15.8%</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">今日节约</div>
                                <div class="metric-value">245 kWh</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 定时任务 -->
                <div class="card mt-6">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i>
                            定时任务管理
                        </h3>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            添加任务
                        </button>
                    </div>
                    <div class="schedule-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>执行时间</th>
                                    <th>动作</th>
                                    <th>状态</th>
                                    <th>下次执行</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>夜间节能模式</td>
                                    <td>每日 22:00</td>
                                    <td>降低风量至60%</td>
                                    <td><span class="status-indicator status-success">启用</span></td>
                                    <td>今日 22:00</td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>工作模式启动</td>
                                    <td>每日 08:00</td>
                                    <td>恢复正常运行参数</td>
                                    <td><span class="status-indicator status-success">启用</span></td>
                                    <td>明日 08:00</td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <style>
        .automation-container {
            padding: var(--spacing-lg);
        }
        
        .strategy-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .strategy-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .strategy-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .strategy-desc {
            font-size: 0.875rem;
            color: var(--text-muted);
        }
        
        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-tertiary);
            transition: var(--transition);
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: var(--transition);
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(20px);
        }
        
        .efficiency-metrics {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .metric-item {
            text-align: center;
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }
        
        .metric-value {
            font-family: var(--font-mono);
            font-size: 2rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .schedule-table {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .data-table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }
        
        .data-table td {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .data-table tr:hover {
            background: var(--bg-hover);
        }
    </style>
    
    <!-- 脚本文件 -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const pageTitle = document.getElementById('pageTitle');
                const breadcrumb = document.getElementById('breadcrumb');
                
                if (pageTitle) {
                    pageTitle.textContent = '自动控制';
                }
                
                if (breadcrumb) {
                    breadcrumb.innerHTML = `
                        <span class="breadcrumb-item">首页</span>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                        <span class="breadcrumb-item active">自动控制</span>
                    `;
                }
            }, 500);
        });
    </script>
</body>
</html>
