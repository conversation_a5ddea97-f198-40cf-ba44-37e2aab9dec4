<!-- 洁净空调系统 - 导航栏组件 -->
<!-- 侧边导航栏 -->
<nav class="sidebar" id="sidebar">
  <!-- 系统标题 -->
  <div class="sidebar-header">
    <div class="system-logo">
      <i class="fas fa-wind"></i>
      <span class="system-name">洁净空调系统</span>
    </div>
  </div>

  <!-- 导航菜单 -->
  <div class="sidebar-menu">
    <ul class="nav-list">
      <li class="nav-item">
        <a href="index.html" class="nav-link" data-page="dashboard">
          <i class="nav-icon fas fa-tachometer-alt"></i>
          <span class="nav-text">首页</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="monitoring.html" class="nav-link" data-page="monitoring">
          <i class="nav-icon fas fa-chart-line"></i>
          <span class="nav-text">环境监测</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="equipment.html" class="nav-link" data-page="equipment">
          <i class="nav-icon fas fa-cogs"></i>
          <span class="nav-text">设备管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="airflow.html" class="nav-link" data-page="airflow">
          <i class="nav-icon fas fa-wind"></i>
          <span class="nav-text">风量控制</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="pressure.html" class="nav-link" data-page="pressure">
          <i class="nav-icon fas fa-compress-arrows-alt"></i>
          <span class="nav-text">压差管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="automation.html" class="nav-link" data-page="automation">
          <i class="nav-icon fas fa-robot"></i>
          <span class="nav-text">自动控制</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="compliance.html" class="nav-link" data-page="compliance">
          <i class="nav-icon fas fa-certificate"></i>
          <span class="nav-text">合规管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="maintenance.html" class="nav-link" data-page="maintenance">
          <i class="nav-icon fas fa-wrench"></i>
          <span class="nav-text">维护管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="ai-models.html" class="nav-link" data-page="ai-models">
          <i class="nav-icon fas fa-brain"></i>
          <span class="nav-text">AI模型</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="dashboard-large.html" class="nav-link" data-page="dashboard-large">
          <i class="nav-icon fas fa-tv"></i>
          <span class="nav-text">大屏展示</span>
        </a>
      </li>
    </ul>
  </div>
</nav>

<!-- 导航栏样式 -->
<style>
/* 侧边导航栏 - 参考蓝色主题 */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 200px;
  height: 100vh;
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: var(--transition);
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
}

.system-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
}

.system-logo i {
  font-size: 1.5rem;
  color: white;
}

.sidebar-menu {
  flex: 1;
  padding: var(--spacing-md) 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 2px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 12px var(--spacing-lg);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: rgba(255, 255, 255, 0.3);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-left-color: white;
  font-weight: 600;
}

.nav-icon {
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.nav-text {
  flex: 1;
}

/* 主内容区域适配 */
.main-content {
  margin-left: 200px !important;
  margin-top: 0 !important;
  min-height: 100vh;
  transition: var(--transition);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0 !important;
  }

  .system-name {
    display: none;
  }
}
</style>




<!-- 导航栏脚本 -->
<script>
// 等待DOM加载完成后执行
setTimeout(function() {
  // 设置当前页面的导航高亮
  const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    link.classList.remove('active');
    const page = link.getAttribute('data-page');
    if (page === currentPage || (currentPage === 'index' && page === 'dashboard')) {
      link.classList.add('active');
    }
  });
}, 100);
</script>
