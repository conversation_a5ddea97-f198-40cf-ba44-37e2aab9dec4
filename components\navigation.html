<!-- 洁净空调系统 - 顶部导航栏组件 -->
<nav class="top-navbar">
  <!-- 左侧Logo和系统名称 -->
  <div class="navbar-left">
    <div class="system-logo">
      <i class="fas fa-wind"></i>
      <span class="system-name">洁净空调管理系统</span>
    </div>
  </div>

  <!-- 右侧用户信息和操作 -->
  <div class="navbar-right">
    <!-- 通知按钮 -->
    <div class="navbar-item">
      <button class="notification-btn" id="notificationBtn">
        <i class="fas fa-bell"></i>
        <span class="notification-badge">3</span>
      </button>
    </div>

    <!-- 用户信息 -->
    <div class="navbar-item">
      <div class="user-dropdown" id="userDropdown">
        <div class="user-info">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <span class="user-name">管理员</span>
          <i class="fas fa-chevron-down dropdown-arrow"></i>
        </div>
        <div class="dropdown-menu" id="userDropdownMenu">
          <a href="#" class="dropdown-item">
            <i class="fas fa-user-cog"></i>
            个人设置
          </a>
          <a href="#" class="dropdown-item">
            <i class="fas fa-key"></i>
            修改密码
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-sign-out-alt"></i>
            退出登录
          </a>
        </div>
      </div>
    </div>
  </div>
</nav>

<!-- 侧边导航栏 -->
<nav class="sidebar" id="sidebar">

  <!-- 导航菜单 -->
  <div class="sidebar-menu">
    <ul class="nav-list">
      <li class="nav-item">
        <a href="index.html" class="nav-link" data-page="dashboard">
          <i class="nav-icon fas fa-tachometer-alt"></i>
          <span class="nav-text">首页</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="monitoring.html" class="nav-link" data-page="monitoring">
          <i class="nav-icon fas fa-chart-line"></i>
          <span class="nav-text">环境监测</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="equipment.html" class="nav-link" data-page="equipment">
          <i class="nav-icon fas fa-cogs"></i>
          <span class="nav-text">设备管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="airflow.html" class="nav-link" data-page="airflow">
          <i class="nav-icon fas fa-wind"></i>
          <span class="nav-text">风量控制</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="pressure.html" class="nav-link" data-page="pressure">
          <i class="nav-icon fas fa-compress-arrows-alt"></i>
          <span class="nav-text">压差管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="automation.html" class="nav-link" data-page="automation">
          <i class="nav-icon fas fa-robot"></i>
          <span class="nav-text">自动控制</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="compliance.html" class="nav-link" data-page="compliance">
          <i class="nav-icon fas fa-certificate"></i>
          <span class="nav-text">合规管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="maintenance.html" class="nav-link" data-page="maintenance">
          <i class="nav-icon fas fa-wrench"></i>
          <span class="nav-text">维护管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="ai-models.html" class="nav-link" data-page="ai-models">
          <i class="nav-icon fas fa-brain"></i>
          <span class="nav-text">AI模型</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="dashboard-large.html" class="nav-link" data-page="dashboard-large">
          <i class="nav-icon fas fa-tv"></i>
          <span class="nav-text">大屏展示</span>
        </a>
      </li>
    </ul>
  </div>
</nav>

<!-- 导航栏样式 -->
<style>
/* 顶部导航栏 */
.top-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-left {
  display: flex;
  align-items: center;
}

.system-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.system-logo i {
  font-size: 1.5rem;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.navbar-item {
  position: relative;
}

.notification-btn {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-dropdown {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 0.75rem;
  transition: var(--transition);
}

.user-dropdown.active .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  display: none;
  z-index: 1000;
  margin-top: var(--spacing-sm);
}

.user-dropdown.active .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* 侧边导航栏 */
.sidebar {
  position: fixed;
  left: 0;
  top: 60px;
  width: 240px;
  height: calc(100vh - 60px);
  background: white;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: var(--transition);
  overflow-y: auto;
}

.sidebar-menu {
  flex: 1;
  padding: var(--spacing-md) 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-link.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
  border-right: 3px solid #667eea;
}

.nav-icon {
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.nav-text {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .system-name {
    display: none;
  }
}
</style>

<!-- 导航栏脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 用户下拉菜单
  const userDropdown = document.getElementById('userDropdown');
  const userDropdownMenu = document.getElementById('userDropdownMenu');

  if (userDropdown) {
    userDropdown.addEventListener('click', function(e) {
      e.stopPropagation();
      this.classList.toggle('active');
    });
  }

  // 点击其他地方关闭下拉菜单
  document.addEventListener('click', function() {
    if (userDropdown) {
      userDropdown.classList.remove('active');
    }
  });

  // 通知按钮
  const notificationBtn = document.getElementById('notificationBtn');
  if (notificationBtn) {
    notificationBtn.addEventListener('click', function() {
      // 这里可以添加通知功能
      console.log('显示通知');
    });
  }

  // 设置当前页面的导航高亮
  setActiveNavigation();
});

// 设置当前页面的导航高亮
function setActiveNavigation() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    link.classList.remove('active');
    const href = link.getAttribute('href');
    if (href === currentPage || (currentPage === '' && href === 'index.html')) {
      link.classList.add('active');
    }
  });
}
</script>
      
      <li class="nav-item">
        <a href="dashboard-large.html" class="nav-link" data-page="dashboard-large">
          <i class="fas fa-tv"></i>
          <span class="nav-text">大屏展示</span>
        </a>
      </li>
      
      <li class="nav-item">
        <a href="ai-models.html" class="nav-link" data-page="ai-models">
          <i class="fas fa-brain"></i>
          <span class="nav-text">AI模型</span>
        </a>
      </li>
    </ul>

    <!-- 系统状态 -->
    <div class="system-status">
      <div class="status-item">
        <div class="status-indicator status-success">
          <i class="fas fa-circle"></i>
        </div>
        <span class="status-text">系统正常</span>
      </div>
      <div class="status-item">
        <div class="status-value">
          <span class="metric-value">23.5</span>
          <span class="metric-unit">°C</span>
        </div>
        <span class="status-label">环境温度</span>
      </div>
    </div>
  </div>

  <!-- 底部信息 -->
  <div class="sidebar-footer">
    <div class="version-info">
      <span class="version">v2.1.0</span>
      <span class="build">Build 20241218</span>
    </div>
  </div>
</nav>

<!-- 导航栏样式 -->
<style>
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 280px;
  height: 100vh;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: var(--transition);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--primary-color);
}

.logo i {
  font-size: 1.5rem;
}

.logo-text {
  font-size: 1.125rem;
  font-weight: 600;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .logo-text {
  opacity: 0;
  width: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.125rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md) 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.user-avatar {
  font-size: 2rem;
  color: var(--primary-color);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 0.75rem;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .user-details {
  display: none;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-link.active {
  background: rgba(30, 64, 175, 0.1);
  color: var(--primary-color);
  border-right: 3px solid var(--primary-color);
}

.nav-link i {
  font-size: 1.125rem;
  width: 20px;
  text-align: center;
}

.nav-text {
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.system-status {
  margin: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.status-value {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.status-value .metric-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.status-value .metric-unit {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.status-label {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.sidebar.collapsed .system-status {
  display: none;
}

.sidebar-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.version-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.sidebar.collapsed .version-info {
  display: none;
}

/* 主内容区域适配 */
.main-content {
  margin-left: 280px;
  min-height: 100vh;
  transition: var(--transition);
}

.sidebar.collapsed + .main-content {
  margin-left: 70px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .sidebar.collapsed + .main-content {
    margin-left: 0;
  }
}
</style>

<!-- 导航栏脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const sidebar = document.getElementById('sidebar');
  const sidebarToggle = document.getElementById('sidebarToggle');
  const navLinks = document.querySelectorAll('.nav-link');
  
  // 侧边栏折叠/展开
  sidebarToggle.addEventListener('click', function() {
    sidebar.classList.toggle('collapsed');
    localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
  });
  
  // 恢复侧边栏状态
  const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
  if (isCollapsed) {
    sidebar.classList.add('collapsed');
  }
  
  // 设置当前页面的导航高亮
  const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
  navLinks.forEach(link => {
    const page = link.getAttribute('data-page');
    if (page === currentPage || (currentPage === 'index' && page === 'dashboard')) {
      link.classList.add('active');
    }
  });
  
  // 移动端导航
  if (window.innerWidth <= 768) {
    sidebar.classList.add('collapsed');
    
    sidebarToggle.addEventListener('click', function(e) {
      e.stopPropagation();
      sidebar.classList.toggle('mobile-open');
    });
    
    document.addEventListener('click', function(e) {
      if (!sidebar.contains(e.target)) {
        sidebar.classList.remove('mobile-open');
      }
    });
  }
});
</script>
