<!-- 洁净空调系统 - 导航栏组件 -->
<!-- 顶部导航栏 -->
<nav class="top-navbar">
  <!-- 左侧系统信息 -->
  <div class="navbar-left">
    <div class="system-logo">
      <i class="fas fa-wind"></i>
      <span class="system-name">洁净空调系统</span>
    </div>
  </div>

  <!-- 右侧用户信息 -->
  <div class="navbar-right">
    <!-- 通知按钮 -->
    <div class="navbar-item">
      <button class="notification-btn" id="notificationBtn">
        <i class="fas fa-bell"></i>
        <span class="notification-badge">3</span>
      </button>
    </div>

    <!-- 用户信息 -->
    <div class="navbar-item">
      <div class="user-dropdown" id="userDropdown">
        <div class="user-info">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <span class="user-name">管理员</span>
          <i class="fas fa-chevron-down dropdown-arrow"></i>
        </div>
        <div class="dropdown-menu" id="userDropdownMenu">
          <a href="#" class="dropdown-item">
            <i class="fas fa-user-cog"></i>
            个人设置
          </a>
          <a href="#" class="dropdown-item">
            <i class="fas fa-key"></i>
            修改密码
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-sign-out-alt"></i>
            退出登录
          </a>
        </div>
      </div>
    </div>
  </div>
</nav>

<!-- 侧边导航栏 -->
<nav class="sidebar" id="sidebar">
  <!-- 导航菜单标题 -->
  <div class="sidebar-header">
    <div class="menu-title">
      <i class="fas fa-bars"></i>
      <span>功能菜单</span>
    </div>
  </div>

  <!-- 导航菜单 -->
  <div class="sidebar-menu">
    <ul class="nav-list">
      <li class="nav-item">
        <a href="index.html" class="nav-link" data-page="dashboard">
          <i class="nav-icon fas fa-tachometer-alt"></i>
          <span class="nav-text">首页</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="monitoring.html" class="nav-link" data-page="monitoring">
          <i class="nav-icon fas fa-chart-line"></i>
          <span class="nav-text">环境监测</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="equipment.html" class="nav-link" data-page="equipment">
          <i class="nav-icon fas fa-cogs"></i>
          <span class="nav-text">设备管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="airflow.html" class="nav-link" data-page="airflow">
          <i class="nav-icon fas fa-wind"></i>
          <span class="nav-text">风量控制</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="pressure.html" class="nav-link" data-page="pressure">
          <i class="nav-icon fas fa-compress-arrows-alt"></i>
          <span class="nav-text">压差管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="automation.html" class="nav-link" data-page="automation">
          <i class="nav-icon fas fa-robot"></i>
          <span class="nav-text">自动控制</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="compliance.html" class="nav-link" data-page="compliance">
          <i class="nav-icon fas fa-certificate"></i>
          <span class="nav-text">合规管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="maintenance.html" class="nav-link" data-page="maintenance">
          <i class="nav-icon fas fa-wrench"></i>
          <span class="nav-text">维护管理</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="ai-models.html" class="nav-link" data-page="ai-models">
          <i class="nav-icon fas fa-brain"></i>
          <span class="nav-text">AI模型</span>
        </a>
      </li>

      <li class="nav-item">
        <a href="dashboard-large.html" class="nav-link" data-page="dashboard-large">
          <i class="nav-icon fas fa-tv"></i>
          <span class="nav-text">大屏展示</span>
        </a>
      </li>
    </ul>
  </div>
</nav>

<!-- 导航栏样式 -->
<style>
/* 顶部导航栏 - 参考蓝色主题 */
.top-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  z-index: 1001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-left {
  display: flex;
  align-items: center;
}

.system-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
}

.system-logo i {
  font-size: 1.5rem;
  color: white;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.navbar-item {
  position: relative;
}

.notification-btn {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.notification-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-dropdown {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: white;
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 0.75rem;
  transition: var(--transition);
}

.user-dropdown.active .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  display: none;
  z-index: 1000;
  margin-top: var(--spacing-sm);
}

.user-dropdown.active .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* 侧边导航栏 - 参考蓝色主题 */
.sidebar {
  position: fixed;
  left: 0;
  top: 60px;
  width: 200px;
  height: calc(100vh - 60px);
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  transition: var(--transition);
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
}

.menu-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.menu-title i {
  font-size: 1rem;
}

.sidebar-menu {
  flex: 1;
  padding: var(--spacing-md) 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 2px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 12px var(--spacing-lg);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: var(--transition);
  position: relative;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: rgba(255, 255, 255, 0.3);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-left-color: white;
  font-weight: 600;
}

.nav-icon {
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.nav-text {
  flex: 1;
}

/* 主内容区域适配 */
.main-content {
  margin-left: 200px !important;
  margin-top: 60px !important;
  min-height: calc(100vh - 60px);
  transition: var(--transition);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0 !important;
    margin-top: 60px !important;
  }

  .system-name {
    display: none;
  }
}
</style>




<!-- 导航栏脚本 -->
<script>
// 等待DOM加载完成后执行
setTimeout(function() {
  // 设置当前页面的导航高亮
  const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    link.classList.remove('active');
    const page = link.getAttribute('data-page');
    if (page === currentPage || (currentPage === 'index' && page === 'dashboard')) {
      link.classList.add('active');
    }
  });

  // 初始化用户下拉菜单
  const userDropdown = document.getElementById('userDropdown');
  const userDropdownMenu = document.getElementById('userDropdownMenu');

  if (userDropdown) {
    userDropdown.addEventListener('click', function(e) {
      e.stopPropagation();
      userDropdown.classList.toggle('active');
    });
  }

  // 点击其他地方关闭下拉菜单
  document.addEventListener('click', function() {
    if (userDropdown) {
      userDropdown.classList.remove('active');
    }
  });

  // 通知按钮点击事件
  const notificationBtn = document.getElementById('notificationBtn');
  if (notificationBtn) {
    notificationBtn.addEventListener('click', function() {
      // 这里可以添加通知功能
      console.log('显示通知');
    });
  }
}, 100);
</script>
