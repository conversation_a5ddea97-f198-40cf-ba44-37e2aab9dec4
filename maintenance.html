<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 维护管理</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                <h1>维护管理</h1>
                <p class="page-description">设备维护工单与保养计划管理</p>
            </div>
        </div>

        <!-- 维护管理内容 -->
        <div class="maintenance-container">
            <div class="container">
                <div class="grid grid-cols-3 gap-6">
                    <!-- 待处理工单 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-clipboard-list"></i>
                                待处理工单
                            </h3>
                        </div>
                        <div class="work-orders">
                            <div class="order-item urgent">
                                <div class="order-header">
                                    <div class="order-id">#WO-2024-001</div>
                                    <div class="order-priority">紧急</div>
                                </div>
                                <div class="order-title">FFU-001过滤器更换</div>
                                <div class="order-desc">压差超标，需立即更换高效过滤器</div>
                                <div class="order-meta">
                                    <span class="order-time">2小时前</span>
                                    <span class="order-assignee">张工程师</span>
                                </div>
                                <div class="order-actions">
                                    <button class="btn btn-sm btn-primary">处理</button>
                                </div>
                            </div>
                            <div class="order-item normal">
                                <div class="order-header">
                                    <div class="order-id">#WO-2024-002</div>
                                    <div class="order-priority">普通</div>
                                </div>
                                <div class="order-title">定期保养检查</div>
                                <div class="order-desc">生产区域B月度保养检查</div>
                                <div class="order-meta">
                                    <span class="order-time">1天前</span>
                                    <span class="order-assignee">李技师</span>
                                </div>
                                <div class="order-actions">
                                    <button class="btn btn-sm btn-secondary">查看</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 保养计划 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-calendar-alt"></i>
                                保养计划
                            </h3>
                        </div>
                        <div class="maintenance-schedule">
                            <div class="schedule-item">
                                <div class="schedule-date">
                                    <div class="date-day">20</div>
                                    <div class="date-month">12月</div>
                                </div>
                                <div class="schedule-info">
                                    <div class="schedule-title">过滤器检查</div>
                                    <div class="schedule-desc">区域A高效过滤器</div>
                                    <div class="schedule-time">09:00 - 11:00</div>
                                </div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-date">
                                    <div class="date-day">22</div>
                                    <div class="date-month">12月</div>
                                </div>
                                <div class="schedule-info">
                                    <div class="schedule-title">风机保养</div>
                                    <div class="schedule-desc">FFU-003风机清洁</div>
                                    <div class="schedule-time">14:00 - 16:00</div>
                                </div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-date">
                                    <div class="date-day">25</div>
                                    <div class="date-month">12月</div>
                                </div>
                                <div class="schedule-info">
                                    <div class="schedule-title">管道清洁</div>
                                    <div class="schedule-desc">送风管道清洁</div>
                                    <div class="schedule-time">08:00 - 12:00</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 维护统计 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar"></i>
                                维护统计
                            </h3>
                        </div>
                        <div class="maintenance-stats">
                            <div class="stat-item">
                                <div class="stat-value">15</div>
                                <div class="stat-label">本月工单</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">92%</div>
                                <div class="stat-label">完成率</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">2.5h</div>
                                <div class="stat-label">平均响应时间</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">¥12,500</div>
                                <div class="stat-label">本月成本</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 维护记录 -->
                <div class="card mt-6">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i>
                            维护记录
                        </h3>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            新建工单
                        </button>
                    </div>
                    <div class="maintenance-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>工单号</th>
                                    <th>设备</th>
                                    <th>维护类型</th>
                                    <th>负责人</th>
                                    <th>开始时间</th>
                                    <th>完成时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#WO-2024-001</td>
                                    <td>FFU-001</td>
                                    <td>过滤器更换</td>
                                    <td>张工程师</td>
                                    <td>2024-12-18 09:00</td>
                                    <td>-</td>
                                    <td><span class="status-indicator status-warning">进行中</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看</button>
                                        <button class="btn btn-sm btn-primary">完成</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#WO-2024-002</td>
                                    <td>FFU-002</td>
                                    <td>定期保养</td>
                                    <td>李技师</td>
                                    <td>2024-12-17 14:00</td>
                                    <td>2024-12-17 16:30</td>
                                    <td><span class="status-indicator status-success">已完成</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看</button>
                                        <button class="btn btn-sm btn-primary">报告</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#WO-2024-003</td>
                                    <td>管道系统</td>
                                    <td>清洁维护</td>
                                    <td>王师傅</td>
                                    <td>2024-12-16 08:00</td>
                                    <td>2024-12-16 12:00</td>
                                    <td><span class="status-indicator status-success">已完成</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看</button>
                                        <button class="btn btn-sm btn-primary">报告</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <style>
        .maintenance-container {
            padding: var(--spacing-lg);
        }
        
        .work-orders {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .order-item {
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
            border-left: 4px solid;
        }
        
        .order-item.urgent {
            border-left-color: var(--danger-color);
        }
        
        .order-item.normal {
            border-left-color: var(--info-color);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }
        
        .order-id {
            font-family: var(--font-mono);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .order-priority {
            font-size: 0.75rem;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .order-item.urgent .order-priority {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger-color);
        }
        
        .order-item.normal .order-priority {
            background: rgba(14, 165, 233, 0.1);
            color: var(--info-color);
        }
        
        .order-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .order-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }
        
        .order-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-bottom: var(--spacing-sm);
        }
        
        .maintenance-schedule {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .schedule-item {
            display: flex;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .schedule-date {
            text-align: center;
            min-width: 60px;
        }
        
        .date-day {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }
        
        .date-month {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: var(--spacing-xs);
        }
        
        .schedule-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .schedule-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }
        
        .schedule-time {
            font-size: 0.75rem;
            color: var(--text-muted);
            font-family: var(--font-mono);
        }
        
        .maintenance-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .stat-item {
            text-align: center;
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .stat-value {
            font-family: var(--font-mono);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .stat-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .maintenance-table {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .data-table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }
        
        .data-table td {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .data-table tr:hover {
            background: var(--bg-hover);
        }
    </style>
    
    <!-- 脚本文件 -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const pageTitle = document.getElementById('pageTitle');
                const breadcrumb = document.getElementById('breadcrumb');
                
                if (pageTitle) {
                    pageTitle.textContent = '维护管理';
                }
                
                if (breadcrumb) {
                    breadcrumb.innerHTML = `
                        <span class="breadcrumb-item">首页</span>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                        <span class="breadcrumb-item active">维护管理</span>
                    `;
                }
            }, 500);
        });
    </script>
</body>
</html>
