<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 大屏展示</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
    
    <style>
        /* 大屏专用样式 */
        body {
            margin: 0;
            padding: 0;
            background: var(--bg-primary);
            overflow: hidden;
            font-size: 1.2rem;
        }
        
        .large-dashboard {
            width: 100vw;
            height: 100vh;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
        }
        
        .dashboard-header {
            grid-column: 1 / -1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
        }
        
        .system-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .system-logo {
            font-size: 2.5rem;
            color: var(--primary-color);
        }
        
        .title-text {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .system-time {
            text-align: right;
            font-family: var(--font-mono);
        }
        
        .current-time {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1;
        }
        
        .current-date {
            font-size: 1.2rem;
            color: var(--text-muted);
            margin-top: var(--spacing-xs);
        }
        
        .dashboard-panel {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
        }
        
        .panel-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .panel-icon {
            font-size: 1.8rem;
            color: var(--primary-color);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-lg);
            flex: 1;
        }
        
        .large-metric {
            text-align: center;
            padding: var(--spacing-lg);
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }
        
        .metric-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }
        
        .metric-icon.temperature { color: #ef4444; }
        .metric-icon.humidity { color: #3b82f6; }
        .metric-icon.pressure { color: #10b981; }
        .metric-icon.particles { color: #f59e0b; }
        
        .metric-value {
            font-family: var(--font-mono);
            font-size: 3rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
            margin-bottom: var(--spacing-sm);
        }
        
        .metric-unit {
            font-size: 1.2rem;
            color: var(--text-muted);
            margin-left: var(--spacing-sm);
        }
        
        .metric-label {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }
        
        .metric-status {
            font-size: 1rem;
            font-weight: 500;
        }
        
        .status-normal { color: var(--success-color); }
        .status-warning { color: var(--warning-color); }
        .status-danger { color: var(--danger-color); }
        
        .chart-panel {
            flex: 1;
            position: relative;
        }
        
        .chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        
        .zone-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .zone-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .zone-info {
            flex: 1;
        }
        
        .zone-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .zone-details {
            font-size: 1rem;
            color: var(--text-secondary);
        }
        
        .zone-status {
            font-size: 1.1rem;
            font-weight: 500;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-sm);
        }
        
        .alert-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border-left: 4px solid;
        }
        
        .alert-item.warning { border-left-color: var(--warning-color); }
        .alert-item.danger { border-left-color: var(--danger-color); }
        .alert-item.info { border-left-color: var(--info-color); }
        
        .alert-icon {
            font-size: 2rem;
            width: 60px;
            text-align: center;
        }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .alert-desc {
            font-size: 1rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }
        
        .alert-time {
            font-size: 0.9rem;
            color: var(--text-muted);
        }
        
        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .slide-up {
            animation: slideUp 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="large-dashboard">
        <!-- 系统标题栏 -->
        <div class="dashboard-header">
            <div class="system-title">
                <div class="system-logo">
                    <i class="fas fa-wind"></i>
                </div>
                <div class="title-text">洁净空调系统监控中心</div>
            </div>
            <div class="system-time">
                <div class="current-time" id="largeTime">14:25:30</div>
                <div class="current-date" id="largeDate">2024年12月18日 星期三</div>
            </div>
        </div>
        
        <!-- 环境参数面板 -->
        <div class="dashboard-panel">
            <div class="panel-title">
                <i class="panel-icon fas fa-thermometer-half"></i>
                环境参数
            </div>
            <div class="metrics-grid">
                <div class="large-metric">
                    <div class="metric-icon temperature">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <div class="metric-value">23.5<span class="metric-unit">°C</span></div>
                    <div class="metric-label">环境温度</div>
                    <div class="metric-status status-normal">正常</div>
                </div>
                <div class="large-metric">
                    <div class="metric-icon humidity">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div class="metric-value">52<span class="metric-unit">%</span></div>
                    <div class="metric-label">相对湿度</div>
                    <div class="metric-status status-normal">正常</div>
                </div>
                <div class="large-metric">
                    <div class="metric-icon pressure">
                        <i class="fas fa-compress-arrows-alt"></i>
                    </div>
                    <div class="metric-value">15.2<span class="metric-unit">Pa</span></div>
                    <div class="metric-label">压差</div>
                    <div class="metric-status status-normal">正常</div>
                </div>
                <div class="large-metric">
                    <div class="metric-icon particles">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <div class="metric-value">3,520<span class="metric-unit">个/m³</span></div>
                    <div class="metric-label">颗粒物浓度</div>
                    <div class="metric-status status-normal">ISO 5</div>
                </div>
            </div>
        </div>
        
        <!-- 趋势图表面板 -->
        <div class="dashboard-panel">
            <div class="panel-title">
                <i class="panel-icon fas fa-chart-line"></i>
                实时趋势
            </div>
            <div class="chart-panel">
                <div class="chart-container">
                    <canvas id="largeTrendChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 设备状态面板 -->
        <div class="dashboard-panel">
            <div class="panel-title">
                <i class="panel-icon fas fa-cogs"></i>
                设备状态
            </div>
            <div class="chart-panel">
                <div class="chart-container">
                    <canvas id="largeEquipmentChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 区域状态面板 -->
        <div class="dashboard-panel">
            <div class="panel-title">
                <i class="panel-icon fas fa-map-marked-alt"></i>
                区域状态
            </div>
            <div class="zone-list">
                <div class="zone-item slide-up">
                    <div class="zone-info">
                        <div class="zone-name">生产区域A</div>
                        <div class="zone-details">ISO Class 5 | 23.2°C | 51%RH</div>
                    </div>
                    <div class="zone-status status-normal">正常</div>
                </div>
                <div class="zone-item slide-up">
                    <div class="zone-info">
                        <div class="zone-name">生产区域B</div>
                        <div class="zone-details">ISO Class 6 | 24.1°C | 48%RH</div>
                    </div>
                    <div class="zone-status status-warning">警告</div>
                </div>
                <div class="zone-item slide-up">
                    <div class="zone-info">
                        <div class="zone-name">缓冲区域</div>
                        <div class="zone-details">ISO Class 7 | 22.8°C | 53%RH</div>
                    </div>
                    <div class="zone-status status-normal">正常</div>
                </div>
                <div class="zone-item slide-up">
                    <div class="zone-info">
                        <div class="zone-name">更衣区域</div>
                        <div class="zone-details">ISO Class 8 | 23.5°C | 50%RH</div>
                    </div>
                    <div class="zone-status status-normal">正常</div>
                </div>
            </div>
        </div>
        
        <!-- 报警信息面板 -->
        <div class="dashboard-panel">
            <div class="panel-title">
                <i class="panel-icon fas fa-exclamation-triangle"></i>
                实时报警
            </div>
            <div class="alert-list">
                <div class="alert-item warning slide-up">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">过滤器压差异常</div>
                        <div class="alert-desc">FFU-001 压差超过阈值 250Pa</div>
                        <div class="alert-time">2分钟前</div>
                    </div>
                </div>
                <div class="alert-item info slide-up">
                    <div class="alert-icon">
                        <i class="fas fa-info-circle" style="color: var(--info-color);"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">维护提醒</div>
                        <div class="alert-desc">区域A高效过滤器需要更换</div>
                        <div class="alert-time">1小时前</div>
                    </div>
                </div>
                <div class="alert-item info slide-up">
                    <div class="alert-icon">
                        <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">系统优化完成</div>
                        <div class="alert-desc">自动控制策略已更新</div>
                        <div class="alert-time">3小时前</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="assets/js/dashboard-large.js"></script>
</body>
</html>
