<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 环境监测</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面头部 -->
        <div id="header-container"></div>
        
        <!-- 环境监测内容 -->
        <div class="monitoring-container">
            <!-- 实时传感器数据 -->
            <section class="sensors-section">
                <div class="section-header">
                    <h2 class="section-title">实时传感器数据</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary" id="exportDataBtn">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                        <button class="btn btn-primary" id="addSensorBtn">
                            <i class="fas fa-plus"></i>
                            添加传感器
                        </button>
                    </div>
                </div>
                
                <div class="sensors-grid">
                    <!-- 温度传感器 -->
                    <div class="sensor-card temperature">
                        <div class="sensor-header">
                            <div class="sensor-icon">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <div class="sensor-info">
                                <h3 class="sensor-name">环境温度</h3>
                                <div class="sensor-location">生产区域A - T001</div>
                            </div>
                            <div class="sensor-status online">
                                <i class="fas fa-circle"></i>
                                <span>在线</span>
                            </div>
                        </div>
                        <div class="sensor-value">
                            <span class="value">23.5</span>
                            <span class="unit">°C</span>
                        </div>
                        <div class="sensor-range">
                            <span class="range-label">正常范围: 18-26°C</span>
                            <div class="range-bar">
                                <div class="range-fill" style="left: 68.75%; width: 2px;"></div>
                                <div class="range-normal" style="left: 0%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="sensor-trend">
                            <div class="trend-chart" id="tempTrend"></div>
                            <div class="trend-info">
                                <span class="trend-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    +0.2°C
                                </span>
                                <span class="trend-time">5分钟内</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 湿度传感器 -->
                    <div class="sensor-card humidity">
                        <div class="sensor-header">
                            <div class="sensor-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="sensor-info">
                                <h3 class="sensor-name">相对湿度</h3>
                                <div class="sensor-location">生产区域A - H001</div>
                            </div>
                            <div class="sensor-status online">
                                <i class="fas fa-circle"></i>
                                <span>在线</span>
                            </div>
                        </div>
                        <div class="sensor-value">
                            <span class="value">52</span>
                            <span class="unit">%RH</span>
                        </div>
                        <div class="sensor-range">
                            <span class="range-label">正常范围: 45-65%RH</span>
                            <div class="range-bar">
                                <div class="range-fill" style="left: 35%; width: 2px;"></div>
                                <div class="range-normal" style="left: 22.5%; width: 55%;"></div>
                            </div>
                        </div>
                        <div class="sensor-trend">
                            <div class="trend-chart" id="humidityTrend"></div>
                            <div class="trend-info">
                                <span class="trend-change stable">
                                    <i class="fas fa-minus"></i>
                                    稳定
                                </span>
                                <span class="trend-time">5分钟内</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 压差传感器 -->
                    <div class="sensor-card pressure">
                        <div class="sensor-header">
                            <div class="sensor-icon">
                                <i class="fas fa-compress-arrows-alt"></i>
                            </div>
                            <div class="sensor-info">
                                <h3 class="sensor-name">压差</h3>
                                <div class="sensor-location">生产区域A - P001</div>
                            </div>
                            <div class="sensor-status online">
                                <i class="fas fa-circle"></i>
                                <span>在线</span>
                            </div>
                        </div>
                        <div class="sensor-value">
                            <span class="value">15.2</span>
                            <span class="unit">Pa</span>
                        </div>
                        <div class="sensor-range">
                            <span class="range-label">正常范围: 10-25Pa</span>
                            <div class="range-bar">
                                <div class="range-fill" style="left: 34.7%; width: 2px;"></div>
                                <div class="range-normal" style="left: 0%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="sensor-trend">
                            <div class="trend-chart" id="pressureTrend"></div>
                            <div class="trend-info">
                                <span class="trend-change positive">
                                    <i class="fas fa-arrow-up"></i>
                                    +1.2Pa
                                </span>
                                <span class="trend-time">5分钟内</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 风速传感器 -->
                    <div class="sensor-card airflow">
                        <div class="sensor-header">
                            <div class="sensor-icon">
                                <i class="fas fa-wind"></i>
                            </div>
                            <div class="sensor-info">
                                <h3 class="sensor-name">风速</h3>
                                <div class="sensor-location">送风口 - V001</div>
                            </div>
                            <div class="sensor-status online">
                                <i class="fas fa-circle"></i>
                                <span>在线</span>
                            </div>
                        </div>
                        <div class="sensor-value">
                            <span class="value">0.45</span>
                            <span class="unit">m/s</span>
                        </div>
                        <div class="sensor-range">
                            <span class="range-label">正常范围: 0.3-0.6m/s</span>
                            <div class="range-bar">
                                <div class="range-fill" style="left: 50%; width: 2px;"></div>
                                <div class="range-normal" style="left: 0%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="sensor-trend">
                            <div class="trend-chart" id="airflowTrend"></div>
                            <div class="trend-info">
                                <span class="trend-change negative">
                                    <i class="fas fa-arrow-down"></i>
                                    -0.05m/s
                                </span>
                                <span class="trend-time">5分钟内</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 颗粒物传感器 -->
                    <div class="sensor-card particles">
                        <div class="sensor-header">
                            <div class="sensor-icon">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="sensor-info">
                                <h3 class="sensor-name">颗粒物浓度</h3>
                                <div class="sensor-location">生产区域A - PM001</div>
                            </div>
                            <div class="sensor-status online">
                                <i class="fas fa-circle"></i>
                                <span>在线</span>
                            </div>
                        </div>
                        <div class="sensor-value">
                            <span class="value">3,520</span>
                            <span class="unit">个/m³</span>
                        </div>
                        <div class="sensor-range">
                            <span class="range-label">ISO Class 5: ≤3,520个/m³</span>
                            <div class="range-bar">
                                <div class="range-fill" style="left: 100%; width: 2px;"></div>
                                <div class="range-normal" style="left: 0%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="sensor-trend">
                            <div class="trend-chart" id="particlesTrend"></div>
                            <div class="trend-info">
                                <span class="trend-change positive">
                                    <i class="fas fa-arrow-down"></i>
                                    -150个/m³
                                </span>
                                <span class="trend-time">5分钟内</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- CO2传感器 -->
                    <div class="sensor-card co2">
                        <div class="sensor-header">
                            <div class="sensor-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div class="sensor-info">
                                <h3 class="sensor-name">CO₂浓度</h3>
                                <div class="sensor-location">生产区域A - CO2001</div>
                            </div>
                            <div class="sensor-status online">
                                <i class="fas fa-circle"></i>
                                <span>在线</span>
                            </div>
                        </div>
                        <div class="sensor-value">
                            <span class="value">420</span>
                            <span class="unit">ppm</span>
                        </div>
                        <div class="sensor-range">
                            <span class="range-label">正常范围: 350-1000ppm</span>
                            <div class="range-bar">
                                <div class="range-fill" style="left: 10.8%; width: 2px;"></div>
                                <div class="range-normal" style="left: 0%; width: 100%;"></div>
                            </div>
                        </div>
                        <div class="sensor-trend">
                            <div class="trend-chart" id="co2Trend"></div>
                            <div class="trend-info">
                                <span class="trend-change stable">
                                    <i class="fas fa-minus"></i>
                                    稳定
                                </span>
                                <span class="trend-time">5分钟内</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 历史趋势图表 -->
            <section class="trends-section">
                <div class="section-header">
                    <h2 class="section-title">历史趋势分析</h2>
                    <div class="chart-controls">
                        <select class="form-select" id="parameterSelect">
                            <option value="all">全部参数</option>
                            <option value="temperature">温度</option>
                            <option value="humidity">湿度</option>
                            <option value="pressure">压差</option>
                            <option value="airflow">风速</option>
                            <option value="particles">颗粒物</option>
                            <option value="co2">CO₂</option>
                        </select>
                        <select class="form-select" id="timeRangeSelect">
                            <option value="1h">最近1小时</option>
                            <option value="6h">最近6小时</option>
                            <option value="24h" selected>最近24小时</option>
                            <option value="7d">最近7天</option>
                            <option value="30d">最近30天</option>
                        </select>
                        <button class="btn btn-secondary" id="fullscreenBtn">
                            <i class="fas fa-expand"></i>
                            全屏
                        </button>
                    </div>
                </div>
                
                <div class="chart-container">
                    <canvas id="trendsChart"></canvas>
                </div>
            </section>
            
            <!-- 报警阈值设置 -->
            <section class="thresholds-section">
                <div class="section-header">
                    <h2 class="section-title">报警阈值设置</h2>
                    <button class="btn btn-primary" id="saveThresholdsBtn">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                </div>
                
                <div class="thresholds-grid">
                    <div class="threshold-card">
                        <div class="threshold-header">
                            <h3 class="threshold-name">温度阈值</h3>
                            <div class="threshold-toggle">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="threshold-settings">
                            <div class="threshold-range">
                                <label class="form-label">正常范围</label>
                                <div class="range-inputs">
                                    <input type="number" class="form-input" value="18" min="0" max="50" step="0.1">
                                    <span class="range-separator">-</span>
                                    <input type="number" class="form-input" value="26" min="0" max="50" step="0.1">
                                    <span class="range-unit">°C</span>
                                </div>
                            </div>
                            <div class="threshold-actions">
                                <label class="form-label">报警动作</label>
                                <div class="action-checkboxes">
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>系统通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>邮件通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>短信通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>自动调节</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="threshold-card">
                        <div class="threshold-header">
                            <h3 class="threshold-name">湿度阈值</h3>
                            <div class="threshold-toggle">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="threshold-settings">
                            <div class="threshold-range">
                                <label class="form-label">正常范围</label>
                                <div class="range-inputs">
                                    <input type="number" class="form-input" value="45" min="0" max="100" step="1">
                                    <span class="range-separator">-</span>
                                    <input type="number" class="form-input" value="65" min="0" max="100" step="1">
                                    <span class="range-unit">%RH</span>
                                </div>
                            </div>
                            <div class="threshold-actions">
                                <label class="form-label">报警动作</label>
                                <div class="action-checkboxes">
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>系统通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>邮件通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>短信通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>自动调节</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="threshold-card">
                        <div class="threshold-header">
                            <h3 class="threshold-name">压差阈值</h3>
                            <div class="threshold-toggle">
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                        <div class="threshold-settings">
                            <div class="threshold-range">
                                <label class="form-label">正常范围</label>
                                <div class="range-inputs">
                                    <input type="number" class="form-input" value="10" min="0" max="100" step="0.1">
                                    <span class="range-separator">-</span>
                                    <input type="number" class="form-input" value="25" min="0" max="100" step="0.1">
                                    <span class="range-unit">Pa</span>
                                </div>
                            </div>
                            <div class="threshold-actions">
                                <label class="form-label">报警动作</label>
                                <div class="action-checkboxes">
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>系统通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>邮件通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>短信通知</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span>自动调节</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <!-- 环境监测页面样式 -->
    <style>
        .monitoring-container {
            padding: var(--spacing-lg);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .section-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .sensors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        .sensor-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .sensor-card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--border-light);
        }

        .sensor-card.temperature {
            border-left: 4px solid #ef4444;
        }

        .sensor-card.humidity {
            border-left: 4px solid #3b82f6;
        }

        .sensor-card.pressure {
            border-left: 4px solid #10b981;
        }

        .sensor-card.airflow {
            border-left: 4px solid #8b5cf6;
        }

        .sensor-card.particles {
            border-left: 4px solid #f59e0b;
        }

        .sensor-card.co2 {
            border-left: 4px solid #06b6d4;
        }

        .sensor-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .sensor-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: var(--bg-tertiary);
            color: var(--text-secondary);
        }

        .sensor-card.temperature .sensor-icon {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .sensor-card.humidity .sensor-icon {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .sensor-card.pressure .sensor-icon {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .sensor-card.airflow .sensor-icon {
            background: rgba(139, 92, 246, 0.1);
            color: #8b5cf6;
        }

        .sensor-card.particles .sensor-icon {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .sensor-card.co2 .sensor-icon {
            background: rgba(6, 182, 212, 0.1);
            color: #06b6d4;
        }

        .sensor-info {
            flex: 1;
            min-width: 0;
        }

        .sensor-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
        }

        .sensor-location {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .sensor-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sensor-status.online {
            color: var(--success-color);
        }

        .sensor-status.offline {
            color: var(--danger-color);
        }

        .sensor-value {
            display: flex;
            align-items: baseline;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-md);
        }

        .sensor-value .value {
            font-family: var(--font-mono);
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
            color: var(--text-primary);
        }

        .sensor-value .unit {
            font-size: 1rem;
            font-weight: 400;
            color: var(--text-muted);
        }

        .sensor-range {
            margin-bottom: var(--spacing-md);
        }

        .range-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
            display: block;
        }

        .range-bar {
            position: relative;
            height: 6px;
            background: var(--bg-tertiary);
            border-radius: 3px;
            overflow: hidden;
        }

        .range-normal {
            position: absolute;
            top: 0;
            height: 100%;
            background: rgba(5, 150, 105, 0.3);
            border-radius: 3px;
        }

        .range-fill {
            position: absolute;
            top: 0;
            height: 100%;
            background: var(--primary-color);
            border-radius: 3px;
            z-index: 1;
        }

        .sensor-trend {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--spacing-md);
        }

        .trend-chart {
            flex: 1;
            height: 40px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-sm);
            position: relative;
            overflow: hidden;
        }

        .trend-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: var(--spacing-xs);
        }

        .trend-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .trend-change.positive {
            color: var(--success-color);
        }

        .trend-change.negative {
            color: var(--danger-color);
        }

        .trend-change.stable {
            color: var(--text-muted);
        }

        .trend-time {
            font-size: 0.625rem;
            color: var(--text-muted);
        }

        .trends-section {
            margin-bottom: var(--spacing-2xl);
        }

        .chart-controls {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .chart-container {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            height: 400px;
            position: relative;
        }

        .thresholds-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-lg);
        }

        .threshold-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
        }

        .threshold-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-lg);
        }

        .threshold-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .threshold-toggle .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .threshold-toggle .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .threshold-toggle .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--bg-tertiary);
            transition: var(--transition);
            border-radius: 24px;
        }

        .threshold-toggle .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: var(--transition);
            border-radius: 50%;
        }

        .threshold-toggle input:checked + .slider {
            background-color: var(--primary-color);
        }

        .threshold-toggle input:checked + .slider:before {
            transform: translateX(20px);
        }

        .threshold-settings {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .threshold-range {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .range-inputs {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .range-inputs .form-input {
            width: 80px;
        }

        .range-separator {
            color: var(--text-muted);
            font-weight: 500;
        }

        .range-unit {
            font-size: 0.875rem;
            color: var(--text-muted);
            min-width: 40px;
        }

        .threshold-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .action-checkboxes {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .checkbox-label input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sensors-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .monitoring-container {
                padding: var(--spacing-md);
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .sensors-grid {
                grid-template-columns: 1fr;
            }

            .chart-controls {
                flex-wrap: wrap;
            }

            .thresholds-grid {
                grid-template-columns: 1fr;
            }

            .sensor-value .value {
                font-size: 2rem;
            }
        }
    </style>

    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="assets/js/common.js"></script>
    <script src="assets/js/monitoring.js"></script>
</body>
</html>
