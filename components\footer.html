<!-- 洁净空调系统 - 页脚组件 -->
<footer class="page-footer">
  <div class="footer-content">
    <!-- 公司信息 -->
    <div class="footer-section">
      <div class="footer-logo">
        <i class="fas fa-wind"></i>
        <span class="logo-text">洁净空调系统</span>
      </div>
      <p class="footer-desc">
        专业的洁净室环境控制解决方案，为您提供安全、高效、智能的空气净化服务。
      </p>
      <div class="footer-contact">
        <div class="contact-item">
          <i class="fas fa-phone"></i>
          <span>************</span>
        </div>
        <div class="contact-item">
          <i class="fas fa-envelope"></i>
          <span><EMAIL></span>
        </div>
        <div class="contact-item">
          <i class="fas fa-map-marker-alt"></i>
          <span>北京市朝阳区科技园区</span>
        </div>
      </div>
    </div>

    <!-- 产品服务 -->
    <div class="footer-section">
      <h4 class="footer-title">产品服务</h4>
      <ul class="footer-links">
        <li><a href="#" class="footer-link">环境监测系统</a></li>
        <li><a href="#" class="footer-link">设备管理平台</a></li>
        <li><a href="#" class="footer-link">自动控制系统</a></li>
        <li><a href="#" class="footer-link">合规管理工具</a></li>
        <li><a href="#" class="footer-link">AI智能分析</a></li>
        <li><a href="#" class="footer-link">维护保养服务</a></li>
      </ul>
    </div>

    <!-- 解决方案 -->
    <div class="footer-section">
      <h4 class="footer-title">解决方案</h4>
      <ul class="footer-links">
        <li><a href="#" class="footer-link">制药洁净室</a></li>
        <li><a href="#" class="footer-link">电子无尘车间</a></li>
        <li><a href="#" class="footer-link">食品净化工程</a></li>
        <li><a href="#" class="footer-link">医疗洁净手术室</a></li>
        <li><a href="#" class="footer-link">实验室净化</a></li>
        <li><a href="#" class="footer-link">精密制造车间</a></li>
      </ul>
    </div>

    <!-- 技术支持 -->
    <div class="footer-section">
      <h4 class="footer-title">技术支持</h4>
      <ul class="footer-links">
        <li><a href="#" class="footer-link">在线帮助</a></li>
        <li><a href="#" class="footer-link">技术文档</a></li>
        <li><a href="#" class="footer-link">视频教程</a></li>
        <li><a href="#" class="footer-link">常见问题</a></li>
        <li><a href="#" class="footer-link">远程支持</a></li>
        <li><a href="#" class="footer-link">培训服务</a></li>
      </ul>
    </div>

    <!-- 关注我们 -->
    <div class="footer-section">
      <h4 class="footer-title">关注我们</h4>
      <div class="social-links">
        <a href="#" class="social-link" title="微信公众号">
          <i class="fab fa-weixin"></i>
        </a>
        <a href="#" class="social-link" title="新浪微博">
          <i class="fab fa-weibo"></i>
        </a>
        <a href="#" class="social-link" title="LinkedIn">
          <i class="fab fa-linkedin"></i>
        </a>
        <a href="#" class="social-link" title="YouTube">
          <i class="fab fa-youtube"></i>
        </a>
      </div>
      <div class="qr-code">
        <div class="qr-placeholder">
          <i class="fas fa-qrcode"></i>
          <span>扫码关注</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 版权信息 -->
  <div class="footer-bottom">
    <div class="footer-bottom-content">
      <div class="copyright">
        <p>&copy; 2024 洁净空调系统. 保留所有权利.</p>
        <p>京ICP备12345678号-1 | 京公网安备11010502012345号</p>
      </div>
      <div class="footer-bottom-links">
        <a href="#" class="bottom-link">隐私政策</a>
        <span class="separator">|</span>
        <a href="#" class="bottom-link">服务条款</a>
        <span class="separator">|</span>
        <a href="#" class="bottom-link">法律声明</a>
        <span class="separator">|</span>
        <a href="#" class="bottom-link">网站地图</a>
      </div>
    </div>
  </div>
</footer>

<!-- 页脚样式 -->
<style>
.page-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-2xl);
  color: var(--text-secondary);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-2xl) var(--spacing-lg);
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: var(--spacing-2xl);
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--primary-color);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.footer-logo i {
  font-size: 1.5rem;
}

.footer-desc {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.contact-item i {
  width: 16px;
  color: var(--primary-color);
}

.footer-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-link {
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.875rem;
  transition: var(--transition);
}

.footer-link:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-tertiary);
  color: var(--text-muted);
  border-radius: 50%;
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
}

.qr-code {
  margin-top: var(--spacing-md);
}

.qr-placeholder {
  width: 100px;
  height: 100px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  color: var(--text-muted);
}

.qr-placeholder i {
  font-size: 2rem;
}

.qr-placeholder span {
  font-size: 0.75rem;
}

.footer-bottom {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.copyright p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.footer-bottom-links {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.bottom-link {
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.75rem;
  transition: var(--transition);
}

.bottom-link:hover {
  color: var(--primary-color);
}

.separator {
  color: var(--border-color);
  font-size: 0.75rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-lg);
  }
  
  .footer-section:nth-child(4),
  .footer-section:nth-child(5) {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-content {
    padding: var(--spacing-md);
  }
  
  .social-links {
    justify-content: center;
  }
  
  .qr-placeholder {
    margin: 0 auto;
  }
}
</style>
