<!-- 洁净空调系统 - 页面头部组件 -->
<header class="page-header">
  <div class="header-content">
    <!-- 页面标题区域 -->
    <div class="page-title-section">
      <h1 class="page-title" id="pageTitle">系统概览</h1>
      <nav class="breadcrumb" id="breadcrumb">
        <span class="breadcrumb-item">首页</span>
        <i class="fas fa-chevron-right breadcrumb-separator"></i>
        <span class="breadcrumb-item active">系统概览</span>
      </nav>
    </div>

    <!-- 头部操作区域 -->
    <div class="header-actions">
      <!-- 系统状态指示器 -->
      <div class="system-indicators">
        <div class="indicator-item" title="系统状态">
          <i class="fas fa-circle status-icon status-success"></i>
          <span class="indicator-text">正常运行</span>
        </div>
        <div class="indicator-item" title="连接状态">
          <i class="fas fa-wifi status-icon status-success"></i>
          <span class="indicator-text">已连接</span>
        </div>
      </div>

      <!-- 通知中心 -->
      <div class="notification-center">
        <button class="notification-btn" id="notificationBtn">
          <i class="fas fa-bell"></i>
          <span class="notification-badge">3</span>
        </button>
        
        <!-- 通知下拉菜单 -->
        <div class="notification-dropdown" id="notificationDropdown">
          <div class="notification-header">
            <h3>通知中心</h3>
            <button class="mark-all-read">全部已读</button>
          </div>
          <div class="notification-list">
            <div class="notification-item unread">
              <div class="notification-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="notification-content">
                <div class="notification-title">过滤器压差异常</div>
                <div class="notification-desc">FFU-001 过滤器压差超过阈值</div>
                <div class="notification-time">2分钟前</div>
              </div>
            </div>
            <div class="notification-item unread">
              <div class="notification-icon info">
                <i class="fas fa-info-circle"></i>
              </div>
              <div class="notification-content">
                <div class="notification-title">维护提醒</div>
                <div class="notification-desc">区域A高效过滤器需要更换</div>
                <div class="notification-time">1小时前</div>
              </div>
            </div>
            <div class="notification-item">
              <div class="notification-icon success">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="notification-content">
                <div class="notification-title">系统优化完成</div>
                <div class="notification-desc">自动控制策略已更新</div>
                <div class="notification-time">3小时前</div>
              </div>
            </div>
          </div>
          <div class="notification-footer">
            <a href="notifications.html" class="view-all-link">查看全部通知</a>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <button class="action-btn" title="刷新数据" id="refreshBtn">
          <i class="fas fa-sync-alt"></i>
        </button>
        <button class="action-btn" title="导出报告" id="exportBtn">
          <i class="fas fa-download"></i>
        </button>
        <button class="action-btn" title="系统设置" id="settingsBtn">
          <i class="fas fa-cog"></i>
        </button>
      </div>

      <!-- 用户菜单 -->
      <div class="user-menu">
        <button class="user-menu-btn" id="userMenuBtn">
          <div class="user-avatar">
            <i class="fas fa-user-circle"></i>
          </div>
          <div class="user-info">
            <div class="user-name">运维工程师</div>
            <div class="user-role">系统管理员</div>
          </div>
          <i class="fas fa-chevron-down dropdown-arrow"></i>
        </button>
        
        <!-- 用户下拉菜单 -->
        <div class="user-dropdown" id="userDropdown">
          <div class="dropdown-item">
            <i class="fas fa-user"></i>
            <span>个人资料</span>
          </div>
          <div class="dropdown-item">
            <i class="fas fa-cog"></i>
            <span>账户设置</span>
          </div>
          <div class="dropdown-item">
            <i class="fas fa-question-circle"></i>
            <span>帮助中心</span>
          </div>
          <div class="dropdown-divider"></div>
          <div class="dropdown-item logout">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 实时时间显示 -->
  <div class="time-display">
    <div class="current-time" id="currentTime"></div>
    <div class="current-date" id="currentDate"></div>
  </div>
</header>

<!-- 头部样式 -->
<style>
.page-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.page-title-section {
  flex: 1;
  min-width: 0;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--text-muted);
}

.breadcrumb-item.active {
  color: var(--primary-color);
}

.breadcrumb-separator {
  font-size: 0.75rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.system-indicators {
  display: flex;
  gap: var(--spacing-md);
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
}

.status-icon {
  font-size: 0.75rem;
}

.status-icon.status-success {
  color: var(--success-color);
}

.status-icon.status-warning {
  color: var(--warning-color);
}

.status-icon.status-danger {
  color: var(--danger-color);
}

.indicator-text {
  color: var(--text-secondary);
  white-space: nowrap;
}

.notification-center {
  position: relative;
}

.notification-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.125rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.notification-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  min-width: 1.25rem;
  text-align: center;
  line-height: 1;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: none;
  z-index: 1000;
}

.notification-dropdown.show {
  display: block;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.mark-all-read {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.875rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.mark-all-read:hover {
  background: rgba(30, 64, 175, 0.1);
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  transition: var(--transition);
}

.notification-item:hover {
  background: var(--bg-hover);
}

.notification-item.unread {
  background: rgba(30, 64, 175, 0.05);
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.notification-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.notification-icon.info {
  background: rgba(14, 165, 233, 0.1);
  color: var(--info-color);
}

.notification-icon.success {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-color);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.notification-desc {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.notification-time {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.notification-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-all-link:hover {
  text-decoration: underline;
}

.quick-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.user-menu {
  position: relative;
}

.user-menu-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition);
}

.user-menu-btn:hover {
  background: var(--bg-hover);
}

.user-menu .user-avatar {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.user-menu .user-info {
  text-align: left;
}

.user-menu .user-name {
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.2;
}

.user-menu .user-role {
  font-size: 0.75rem;
  color: var(--text-muted);
  line-height: 1.2;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: var(--text-muted);
  transition: var(--transition);
}

.user-menu-btn.active .dropdown-arrow {
  transform: rotate(180deg);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  display: none;
  z-index: 1000;
  padding: var(--spacing-sm) 0;
}

.user-dropdown.show {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.dropdown-item.logout {
  color: var(--danger-color);
}

.dropdown-item.logout:hover {
  background: rgba(220, 38, 38, 0.1);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-color);
  margin: var(--spacing-sm) 0;
}

.time-display {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-lg);
  text-align: right;
  font-size: 0.75rem;
  color: var(--text-muted);
  line-height: 1.2;
}

.current-time {
  font-family: var(--font-mono);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .system-indicators {
    display: none;
  }
  
  .user-menu .user-info {
    display: none;
  }
  
  .time-display {
    position: static;
    text-align: center;
  }
}
</style>

<!-- 头部脚本 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // 通知中心
  const notificationBtn = document.getElementById('notificationBtn');
  const notificationDropdown = document.getElementById('notificationDropdown');
  
  notificationBtn.addEventListener('click', function(e) {
    e.stopPropagation();
    notificationDropdown.classList.toggle('show');
  });
  
  // 用户菜单
  const userMenuBtn = document.getElementById('userMenuBtn');
  const userDropdown = document.getElementById('userDropdown');
  
  userMenuBtn.addEventListener('click', function(e) {
    e.stopPropagation();
    userDropdown.classList.toggle('show');
    userMenuBtn.classList.toggle('active');
  });
  
  // 点击外部关闭下拉菜单
  document.addEventListener('click', function() {
    notificationDropdown.classList.remove('show');
    userDropdown.classList.remove('show');
    userMenuBtn.classList.remove('active');
  });
  
  // 刷新按钮
  const refreshBtn = document.getElementById('refreshBtn');
  refreshBtn.addEventListener('click', function() {
    this.style.transform = 'rotate(360deg)';
    setTimeout(() => {
      this.style.transform = '';
      // 这里可以添加实际的刷新逻辑
      console.log('数据已刷新');
    }, 500);
  });
  
  // 实时时间更新
  function updateTime() {
    const now = new Date();
    const timeElement = document.getElementById('currentTime');
    const dateElement = document.getElementById('currentDate');
    
    if (timeElement && dateElement) {
      timeElement.textContent = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      
      dateElement.textContent = now.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  }
  
  updateTime();
  setInterval(updateTime, 1000);
});
</script>
