// 洁净空调系统 - 大屏展示脚本

let largeTrendChart = null;
let largeEquipmentChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initLargeDashboard();
});

// 初始化大屏仪表盘
function initLargeDashboard() {
    // 初始化图表
    initCharts();
    
    // 启动实时时间更新
    startTimeUpdate();
    
    // 启动数据更新
    startDataUpdate();
    
    // 添加键盘事件监听（ESC退出全屏）
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.fullscreenElement) {
            document.exitFullscreen();
        }
    });
    
    // 自动进入全屏（如果支持）
    setTimeout(() => {
        if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('无法进入全屏模式:', err);
            });
        }
    }, 1000);
}

// 初始化图表
function initCharts() {
    initTrendChart();
    initEquipmentChart();
}

// 初始化趋势图表
function initTrendChart() {
    const ctx = document.getElementById('largeTrendChart');
    if (!ctx) return;
    
    // 生成24小时的时间标签
    const labels = [];
    for (let i = 23; i >= 0; i--) {
        const time = new Date();
        time.setHours(time.getHours() - i);
        labels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
    }
    
    largeTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '温度 (°C)',
                    data: generateSensorData(24, 23.5, 2),
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 8
                },
                {
                    label: '湿度 (%RH)',
                    data: generateSensorData(24, 52, 8),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 8
                },
                {
                    label: '压差 (Pa)',
                    data: generateSensorData(24, 15.2, 3),
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointRadius: 0,
                    pointHoverRadius: 8
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        color: '#cbd5e1',
                        usePointStyle: true,
                        padding: 30,
                        font: {
                            size: 16
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        color: '#334155'
                    },
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            size: 14
                        }
                    }
                },
                y: {
                    grid: {
                        color: '#334155'
                    },
                    ticks: {
                        color: '#94a3b8',
                        font: {
                            size: 14
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// 初始化设备状态图表
function initEquipmentChart() {
    const ctx = document.getElementById('largeEquipmentChart');
    if (!ctx) return;
    
    largeEquipmentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['正常运行', '维护警告', '设备故障'],
            datasets: [{
                data: [85, 12, 3],
                backgroundColor: [
                    '#059669',
                    '#f59e0b',
                    '#dc2626'
                ],
                borderColor: '#1e293b',
                borderWidth: 3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: '#cbd5e1',
                        padding: 30,
                        font: {
                            size: 16
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });
}

// 生成传感器数据
function generateSensorData(points, baseValue, variation) {
    const data = [];
    for (let i = 0; i < points; i++) {
        const noise = (Math.random() - 0.5) * variation * 0.3;
        const trend = Math.sin(i * 0.2) * variation * 0.5;
        data.push(baseValue + trend + noise);
    }
    return data;
}

// 启动时间更新
function startTimeUpdate() {
    function updateTime() {
        const now = new Date();
        const timeElement = document.getElementById('largeTime');
        const dateElement = document.getElementById('largeDate');
        
        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
        
        if (dateElement) {
            dateElement.textContent = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
        }
    }
    
    updateTime();
    setInterval(updateTime, 1000);
}

// 启动数据更新
function startDataUpdate() {
    // 每5秒更新一次环境参数
    setInterval(() => {
        updateEnvironmentMetrics();
    }, 5000);
    
    // 每30秒更新一次图表数据
    setInterval(() => {
        updateChartData();
    }, 30000);
    
    // 每10秒更新一次区域状态
    setInterval(() => {
        updateZoneStatus();
    }, 10000);
    
    // 每15秒更新一次报警信息
    setInterval(() => {
        updateAlerts();
    }, 15000);
}

// 更新环境参数
function updateEnvironmentMetrics() {
    const metrics = [
        { selector: '.temperature .metric-value', base: 23.5, variation: 0.5, unit: '°C', decimals: 1 },
        { selector: '.humidity .metric-value', base: 52, variation: 2, unit: '%', decimals: 0 },
        { selector: '.pressure .metric-value', base: 15.2, variation: 1, unit: 'Pa', decimals: 1 },
        { selector: '.particles .metric-value', base: 3520, variation: 200, unit: '个/m³', decimals: 0 }
    ];
    
    metrics.forEach(metric => {
        const element = document.querySelector(metric.selector);
        if (element) {
            const newValue = metric.base + (Math.random() - 0.5) * metric.variation;
            const formattedValue = newValue.toFixed(metric.decimals);
            element.innerHTML = `${parseFloat(formattedValue).toLocaleString()}<span class="metric-unit">${metric.unit}</span>`;
            
            // 添加闪烁效果表示数据更新
            element.parentElement.classList.add('pulse');
            setTimeout(() => {
                element.parentElement.classList.remove('pulse');
            }, 1000);
        }
    });
}

// 更新图表数据
function updateChartData() {
    if (largeTrendChart) {
        // 移除第一个数据点，添加新的数据点
        const datasets = largeTrendChart.data.datasets;
        const labels = largeTrendChart.data.labels;
        
        // 更新时间标签
        labels.shift();
        const now = new Date();
        labels.push(now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
        
        // 更新数据
        datasets.forEach((dataset, index) => {
            dataset.data.shift();
            const baseValues = [23.5, 52, 15.2];
            const variations = [2, 8, 3];
            const newValue = baseValues[index] + (Math.random() - 0.5) * variations[index];
            dataset.data.push(newValue);
        });
        
        largeTrendChart.update('none');
    }
    
    if (largeEquipmentChart) {
        // 模拟设备状态变化
        const data = largeEquipmentChart.data.datasets[0].data;
        const total = 100;
        const normal = 85 + Math.floor((Math.random() - 0.5) * 10);
        const warning = 12 + Math.floor((Math.random() - 0.5) * 6);
        const error = total - normal - warning;
        
        data[0] = normal;
        data[1] = warning;
        data[2] = error;
        
        largeEquipmentChart.update('none');
    }
}

// 更新区域状态
function updateZoneStatus() {
    const zones = document.querySelectorAll('.zone-item');
    zones.forEach((zone, index) => {
        const statusElement = zone.querySelector('.zone-status');
        const detailsElement = zone.querySelector('.zone-details');
        
        if (statusElement && detailsElement) {
            // 模拟状态变化
            const statuses = ['正常', '警告', '正常', '正常'];
            const statusClasses = ['status-normal', 'status-warning', 'status-normal', 'status-normal'];
            
            // 模拟温度和湿度变化
            const baseTemps = [23.2, 24.1, 22.8, 23.5];
            const baseHumidities = [51, 48, 53, 50];
            const classes = ['ISO Class 5', 'ISO Class 6', 'ISO Class 7', 'ISO Class 8'];
            
            const newTemp = (baseTemps[index] + (Math.random() - 0.5) * 0.5).toFixed(1);
            const newHumidity = Math.round(baseHumidities[index] + (Math.random() - 0.5) * 2);
            
            detailsElement.textContent = `${classes[index]} | ${newTemp}°C | ${newHumidity}%RH`;
            statusElement.textContent = statuses[index];
            statusElement.className = `zone-status ${statusClasses[index]}`;
        }
    });
}

// 更新报警信息
function updateAlerts() {
    const alerts = document.querySelectorAll('.alert-item');
    alerts.forEach(alert => {
        const timeElement = alert.querySelector('.alert-time');
        if (timeElement) {
            // 更新相对时间
            const currentText = timeElement.textContent;
            if (currentText.includes('分钟前')) {
                const minutes = parseInt(currentText) + 1;
                timeElement.textContent = `${minutes}分钟前`;
            } else if (currentText.includes('小时前')) {
                const hours = parseInt(currentText);
                const minutes = Math.floor(Math.random() * 60);
                if (minutes > 0) {
                    timeElement.textContent = `${hours}小时${minutes}分钟前`;
                }
            }
        }
    });
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    if (largeTrendChart) {
        largeTrendChart.destroy();
    }
    if (largeEquipmentChart) {
        largeEquipmentChart.destroy();
    }
});

// 处理全屏变化
document.addEventListener('fullscreenchange', function() {
    if (document.fullscreenElement) {
        console.log('已进入全屏模式');
    } else {
        console.log('已退出全屏模式');
    }
});

// 处理窗口大小变化
window.addEventListener('resize', function() {
    if (largeTrendChart) {
        largeTrendChart.resize();
    }
    if (largeEquipmentChart) {
        largeEquipmentChart.resize();
    }
});

// 添加点击事件处理（可选）
document.addEventListener('click', function(e) {
    // 点击空白区域可以显示/隐藏鼠标指针
    if (e.target === document.body || e.target.classList.contains('large-dashboard')) {
        document.body.style.cursor = document.body.style.cursor === 'none' ? 'default' : 'none';
    }
});

// 鼠标移动时显示指针
document.addEventListener('mousemove', function() {
    document.body.style.cursor = 'default';
    
    // 3秒后自动隐藏指针
    clearTimeout(window.hideMouseTimeout);
    window.hideMouseTimeout = setTimeout(() => {
        if (document.fullscreenElement) {
            document.body.style.cursor = 'none';
        }
    }, 3000);
});
