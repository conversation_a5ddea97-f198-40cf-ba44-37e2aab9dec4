<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 设备管理</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                <h1>设备管理</h1>
                <p class="page-description">FFU设备状态监控与过滤器管理</p>
            </div>
        </div>

        <!-- 设备管理内容 -->
        <div class="equipment-container">
            <!-- FFU设备状态 -->
            <section class="ffu-section">
                <div class="section-header">
                    <h2 class="section-title">FFU设备状态监控</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary" id="exportFFUBtn">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                        <button class="btn btn-primary" id="addFFUBtn">
                            <i class="fas fa-plus"></i>
                            添加设备
                        </button>
                    </div>
                </div>
                
                <div class="ffu-grid">
                    <!-- FFU设备卡片 -->
                    <div class="ffu-card status-normal">
                        <div class="ffu-header">
                            <div class="ffu-info">
                                <h3 class="ffu-name">FFU-001</h3>
                                <div class="ffu-location">生产区域A - 位置1</div>
                            </div>
                            <div class="ffu-status">
                                <span class="status-indicator status-success">
                                    <i class="fas fa-circle"></i>
                                    正常运行
                                </span>
                            </div>
                        </div>
                        
                        <div class="ffu-metrics">
                            <div class="metric-item">
                                <div class="metric-label">风机转速</div>
                                <div class="metric-value">1,450 <span class="unit">RPM</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">压差</div>
                                <div class="metric-value">180 <span class="unit">Pa</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">功率</div>
                                <div class="metric-value">85 <span class="unit">W</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">运行时间</div>
                                <div class="metric-value">2,340 <span class="unit">h</span></div>
                            </div>
                        </div>
                        
                        <div class="ffu-filter">
                            <div class="filter-info">
                                <div class="filter-type">高效过滤器 (H14)</div>
                                <div class="filter-status">
                                    <span class="filter-health good">良好</span>
                                    <span class="filter-remaining">剩余寿命: 85%</span>
                                </div>
                            </div>
                            <div class="filter-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 85%;"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ffu-actions">
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-chart-line"></i>
                                详细数据
                            </button>
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-wrench"></i>
                                维护记录
                            </button>
                            <button class="btn btn-sm btn-primary">
                                <i class="fas fa-cog"></i>
                                设置
                            </button>
                        </div>
                    </div>
                    
                    <!-- 更多FFU设备卡片... -->
                    <div class="ffu-card status-warning">
                        <div class="ffu-header">
                            <div class="ffu-info">
                                <h3 class="ffu-name">FFU-002</h3>
                                <div class="ffu-location">生产区域A - 位置2</div>
                            </div>
                            <div class="ffu-status">
                                <span class="status-indicator status-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    需要维护
                                </span>
                            </div>
                        </div>
                        
                        <div class="ffu-metrics">
                            <div class="metric-item">
                                <div class="metric-label">风机转速</div>
                                <div class="metric-value">1,380 <span class="unit">RPM</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">压差</div>
                                <div class="metric-value">250 <span class="unit">Pa</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">功率</div>
                                <div class="metric-value">92 <span class="unit">W</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">运行时间</div>
                                <div class="metric-value">4,120 <span class="unit">h</span></div>
                            </div>
                        </div>
                        
                        <div class="ffu-filter">
                            <div class="filter-info">
                                <div class="filter-type">高效过滤器 (H14)</div>
                                <div class="filter-status">
                                    <span class="filter-health warning">需更换</span>
                                    <span class="filter-remaining">剩余寿命: 15%</span>
                                </div>
                            </div>
                            <div class="filter-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill warning" style="width: 15%;"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ffu-actions">
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-chart-line"></i>
                                详细数据
                            </button>
                            <button class="btn btn-sm btn-warning">
                                <i class="fas fa-wrench"></i>
                                立即维护
                            </button>
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-cog"></i>
                                设置
                            </button>
                        </div>
                    </div>
                    
                    <div class="ffu-card status-normal">
                        <div class="ffu-header">
                            <div class="ffu-info">
                                <h3 class="ffu-name">FFU-003</h3>
                                <div class="ffu-location">生产区域B - 位置1</div>
                            </div>
                            <div class="ffu-status">
                                <span class="status-indicator status-success">
                                    <i class="fas fa-circle"></i>
                                    正常运行
                                </span>
                            </div>
                        </div>
                        
                        <div class="ffu-metrics">
                            <div class="metric-item">
                                <div class="metric-label">风机转速</div>
                                <div class="metric-value">1,420 <span class="unit">RPM</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">压差</div>
                                <div class="metric-value">195 <span class="unit">Pa</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">功率</div>
                                <div class="metric-value">88 <span class="unit">W</span></div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">运行时间</div>
                                <div class="metric-value">1,850 <span class="unit">h</span></div>
                            </div>
                        </div>
                        
                        <div class="ffu-filter">
                            <div class="filter-info">
                                <div class="filter-type">高效过滤器 (H14)</div>
                                <div class="filter-status">
                                    <span class="filter-health good">良好</span>
                                    <span class="filter-remaining">剩余寿命: 92%</span>
                                </div>
                            </div>
                            <div class="filter-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 92%;"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ffu-actions">
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-chart-line"></i>
                                详细数据
                            </button>
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-wrench"></i>
                                维护记录
                            </button>
                            <button class="btn btn-sm btn-primary">
                                <i class="fas fa-cog"></i>
                                设置
                            </button>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 过滤器库存管理 -->
            <section class="inventory-section">
                <div class="section-header">
                    <h2 class="section-title">过滤器库存管理</h2>
                    <button class="btn btn-primary" id="addInventoryBtn">
                        <i class="fas fa-plus"></i>
                        添加库存
                    </button>
                </div>
                
                <div class="inventory-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>过滤器型号</th>
                                <th>规格</th>
                                <th>当前库存</th>
                                <th>安全库存</th>
                                <th>状态</th>
                                <th>供应商</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="filter-model">
                                        <div class="model-name">HEPA-H14-610x610x69</div>
                                        <div class="model-desc">高效过滤器</div>
                                    </div>
                                </td>
                                <td>610×610×69mm</td>
                                <td>
                                    <span class="inventory-count">25</span>
                                    <span class="inventory-unit">个</span>
                                </td>
                                <td>10</td>
                                <td>
                                    <span class="status-indicator status-success">
                                        <i class="fas fa-circle"></i>
                                        充足
                                    </span>
                                </td>
                                <td>供应商A</td>
                                <td>2024-12-15</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" title="入库">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="出库">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="filter-model">
                                        <div class="model-name">HEPA-H13-610x610x69</div>
                                        <div class="model-desc">高效过滤器</div>
                                    </div>
                                </td>
                                <td>610×610×69mm</td>
                                <td>
                                    <span class="inventory-count">8</span>
                                    <span class="inventory-unit">个</span>
                                </td>
                                <td>15</td>
                                <td>
                                    <span class="status-indicator status-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        不足
                                    </span>
                                </td>
                                <td>供应商B</td>
                                <td>2024-12-10</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" title="入库">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="出库">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="filter-model">
                                        <div class="model-name">PRE-G4-610x610x46</div>
                                        <div class="model-desc">初效过滤器</div>
                                    </div>
                                </td>
                                <td>610×610×46mm</td>
                                <td>
                                    <span class="inventory-count">45</span>
                                    <span class="inventory-unit">个</span>
                                </td>
                                <td>20</td>
                                <td>
                                    <span class="status-indicator status-success">
                                        <i class="fas fa-circle"></i>
                                        充足
                                    </span>
                                </td>
                                <td>供应商A</td>
                                <td>2024-12-12</td>
                                <td>
                                    <div class="table-actions">
                                        <button class="btn btn-sm btn-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-primary" title="入库">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-sm btn-warning" title="出库">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <!-- 设备管理页面样式 -->
    <style>
        .equipment-container {
            padding: var(--spacing-lg);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .section-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .ffu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }

        .ffu-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            transition: var(--transition);
            position: relative;
        }

        .ffu-card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--border-light);
        }

        .ffu-card.status-normal {
            border-left: 4px solid var(--success-color);
        }

        .ffu-card.status-warning {
            border-left: 4px solid var(--warning-color);
        }

        .ffu-card.status-error {
            border-left: 4px solid var(--danger-color);
        }

        .ffu-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-lg);
        }

        .ffu-info {
            flex: 1;
            min-width: 0;
        }

        .ffu-name {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
        }

        .ffu-location {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .ffu-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .metric-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .metric-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .metric-value {
            font-family: var(--font-mono);
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .metric-value .unit {
            font-size: 0.875rem;
            font-weight: 400;
            color: var(--text-muted);
            margin-left: var(--spacing-xs);
        }

        .ffu-filter {
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .filter-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-sm);
        }

        .filter-type {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .filter-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.75rem;
        }

        .filter-health {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .filter-health.good {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
        }

        .filter-health.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .filter-health.danger {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger-color);
        }

        .filter-remaining {
            color: var(--text-muted);
        }

        .filter-progress {
            margin-top: var(--spacing-sm);
        }

        .progress-bar {
            height: 6px;
            background: var(--bg-primary);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--success-color);
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        .progress-fill.warning {
            background: var(--warning-color);
        }

        .progress-fill.danger {
            background: var(--danger-color);
        }

        .ffu-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .inventory-section {
            margin-bottom: var(--spacing-2xl);
        }

        .inventory-table {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .data-table td {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .data-table tr:hover {
            background: var(--bg-hover);
        }

        .filter-model {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .model-name {
            font-weight: 500;
            color: var(--text-primary);
        }

        .model-desc {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .inventory-count {
            font-family: var(--font-mono);
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .inventory-unit {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-left: var(--spacing-xs);
        }

        .table-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .ffu-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .equipment-container {
                padding: var(--spacing-md);
            }

            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-md);
            }

            .ffu-grid {
                grid-template-columns: 1fr;
            }

            .ffu-metrics {
                grid-template-columns: 1fr;
            }

            .inventory-table {
                overflow-x: auto;
            }

            .data-table {
                min-width: 800px;
            }
        }
    </style>

    <!-- 脚本文件 -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/equipment.js"></script>
</body>
</html>
