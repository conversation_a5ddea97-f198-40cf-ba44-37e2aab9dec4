<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 风量控制</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面头部 -->
        <div id="header-container"></div>
        
        <!-- 风量控制内容 -->
        <div class="airflow-container">
            <!-- 实时风量监控 -->
            <section class="airflow-monitoring">
                <div class="section-header">
                    <h2 class="section-title">实时风量监控</h2>
                    <div class="control-mode">
                        <label class="mode-label">控制模式:</label>
                        <select class="form-select" id="controlMode">
                            <option value="auto">自动控制</option>
                            <option value="manual">手动控制</option>
                            <option value="schedule">定时控制</option>
                        </select>
                    </div>
                </div>
                
                <div class="airflow-grid">
                    <!-- 区域A风量控制 -->
                    <div class="airflow-card">
                        <div class="card-header">
                            <h3 class="card-title">生产区域A</h3>
                            <div class="zone-status">
                                <span class="status-indicator status-success">
                                    <i class="fas fa-circle"></i>
                                    正常运行
                                </span>
                            </div>
                        </div>
                        
                        <div class="airflow-metrics">
                            <div class="metric-row">
                                <div class="metric-item">
                                    <div class="metric-label">当前风量</div>
                                    <div class="metric-value">2,850 <span class="unit">m³/h</span></div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">目标风量</div>
                                    <div class="metric-value">3,000 <span class="unit">m³/h</span></div>
                                </div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-item">
                                    <div class="metric-label">换气次数 (ACH)</div>
                                    <div class="metric-value">15.2 <span class="unit">次/h</span></div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">风机频率</div>
                                    <div class="metric-value">45.8 <span class="unit">Hz</span></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="airflow-control">
                            <div class="control-slider">
                                <label class="control-label">风量调节 (0-100%)</label>
                                <div class="slider-container">
                                    <input type="range" class="airflow-slider" min="0" max="100" value="85" id="airflowSliderA">
                                    <div class="slider-value">85%</div>
                                </div>
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-sm btn-secondary" id="decreaseA">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" id="autoA">
                                    <i class="fas fa-magic"></i>
                                    自动
                                </button>
                                <button class="btn btn-sm btn-secondary" id="increaseA">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="airflow-chart">
                            <canvas id="airflowChartA"></canvas>
                        </div>
                    </div>
                    
                    <!-- 区域B风量控制 -->
                    <div class="airflow-card">
                        <div class="card-header">
                            <h3 class="card-title">生产区域B</h3>
                            <div class="zone-status">
                                <span class="status-indicator status-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    需要调整
                                </span>
                            </div>
                        </div>
                        
                        <div class="airflow-metrics">
                            <div class="metric-row">
                                <div class="metric-item">
                                    <div class="metric-label">当前风量</div>
                                    <div class="metric-value">2,150 <span class="unit">m³/h</span></div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">目标风量</div>
                                    <div class="metric-value">2,500 <span class="unit">m³/h</span></div>
                                </div>
                            </div>
                            <div class="metric-row">
                                <div class="metric-item">
                                    <div class="metric-label">换气次数 (ACH)</div>
                                    <div class="metric-value">12.8 <span class="unit">次/h</span></div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">风机频率</div>
                                    <div class="metric-value">38.2 <span class="unit">Hz</span></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="airflow-control">
                            <div class="control-slider">
                                <label class="control-label">风量调节 (0-100%)</label>
                                <div class="slider-container">
                                    <input type="range" class="airflow-slider" min="0" max="100" value="65" id="airflowSliderB">
                                    <div class="slider-value">65%</div>
                                </div>
                            </div>
                            <div class="control-buttons">
                                <button class="btn btn-sm btn-secondary" id="decreaseB">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <button class="btn btn-sm btn-primary" id="autoB">
                                    <i class="fas fa-magic"></i>
                                    自动
                                </button>
                                <button class="btn btn-sm btn-secondary" id="increaseB">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="airflow-chart">
                            <canvas id="airflowChartB"></canvas>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- ACH计算器 -->
            <section class="ach-calculator">
                <div class="section-header">
                    <h2 class="section-title">换气次数计算器</h2>
                    <button class="btn btn-primary" id="calculateACH">
                        <i class="fas fa-calculator"></i>
                        重新计算
                    </button>
                </div>
                
                <div class="calculator-grid">
                    <div class="calculator-inputs">
                        <div class="input-group">
                            <label class="form-label">房间体积 (m³)</label>
                            <input type="number" class="form-input" id="roomVolume" value="200" min="1" step="0.1">
                        </div>
                        <div class="input-group">
                            <label class="form-label">洁净等级</label>
                            <select class="form-select" id="cleanClass">
                                <option value="5">ISO Class 5</option>
                                <option value="6">ISO Class 6</option>
                                <option value="7">ISO Class 7</option>
                                <option value="8">ISO Class 8</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label class="form-label">人员数量</label>
                            <input type="number" class="form-input" id="personCount" value="4" min="0" step="1">
                        </div>
                        <div class="input-group">
                            <label class="form-label">设备热负荷 (kW)</label>
                            <input type="number" class="form-input" id="heatLoad" value="5.2" min="0" step="0.1">
                        </div>
                    </div>
                    
                    <div class="calculator-results">
                        <div class="result-item">
                            <div class="result-label">推荐ACH</div>
                            <div class="result-value">15-20 <span class="unit">次/h</span></div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">最小风量</div>
                            <div class="result-value">3,000 <span class="unit">m³/h</span></div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">推荐风量</div>
                            <div class="result-value">3,600 <span class="unit">m³/h</span></div>
                        </div>
                        <div class="result-item">
                            <div class="result-label">当前效率</div>
                            <div class="result-value">85% <span class="unit">达标</span></div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 气流模式设置 -->
            <section class="airflow-pattern">
                <div class="section-header">
                    <h2 class="section-title">气流模式设置</h2>
                    <div class="pattern-selector">
                        <button class="pattern-btn active" data-pattern="laminar">
                            <i class="fas fa-arrows-alt-v"></i>
                            层流
                        </button>
                        <button class="pattern-btn" data-pattern="turbulent">
                            <i class="fas fa-wind"></i>
                            湍流
                        </button>
                        <button class="pattern-btn" data-pattern="mixed">
                            <i class="fas fa-random"></i>
                            混合
                        </button>
                    </div>
                </div>
                
                <div class="pattern-config">
                    <div class="config-grid">
                        <div class="config-item">
                            <label class="form-label">送风方式</label>
                            <select class="form-select" id="supplyMethod">
                                <option value="ceiling">顶送</option>
                                <option value="side">侧送</option>
                                <option value="floor">地送</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label class="form-label">回风方式</label>
                            <select class="form-select" id="returnMethod">
                                <option value="side">侧回</option>
                                <option value="floor">下回</option>
                                <option value="ceiling">顶回</option>
                            </select>
                        </div>
                        <div class="config-item">
                            <label class="form-label">送风速度 (m/s)</label>
                            <input type="number" class="form-input" id="supplyVelocity" value="0.45" min="0.1" max="2.0" step="0.01">
                        </div>
                        <div class="config-item">
                            <label class="form-label">温度梯度 (°C/m)</label>
                            <input type="number" class="form-input" id="tempGradient" value="0.5" min="0" max="2" step="0.1">
                        </div>
                    </div>
                    
                    <div class="pattern-preview">
                        <div class="preview-title">气流模拟预览</div>
                        <div class="preview-container">
                            <div class="room-layout">
                                <div class="supply-outlets">
                                    <div class="outlet active"></div>
                                    <div class="outlet active"></div>
                                    <div class="outlet active"></div>
                                    <div class="outlet active"></div>
                                </div>
                                <div class="airflow-lines">
                                    <div class="flow-line"></div>
                                    <div class="flow-line"></div>
                                    <div class="flow-line"></div>
                                    <div class="flow-line"></div>
                                </div>
                                <div class="return-inlets">
                                    <div class="inlet"></div>
                                    <div class="inlet"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="assets/js/common.js"></script>
    <script src="assets/js/airflow.js"></script>
</body>
</html>
