<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 压差管理</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面头部 -->
        <div id="header-container"></div>
        
        <!-- 压差管理内容 -->
        <div class="pressure-container">
            <div class="container">
                <div class="grid grid-cols-2 gap-6">
                    <!-- 压差监测 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-compress-arrows-alt"></i>
                                压差监测
                            </h3>
                        </div>
                        <div class="pressure-zones">
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">生产区域A</div>
                                    <div class="zone-pressure">
                                        <span class="pressure-value">15.2</span>
                                        <span class="pressure-unit">Pa</span>
                                    </div>
                                </div>
                                <div class="pressure-status status-normal">正常</div>
                            </div>
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">生产区域B</div>
                                    <div class="zone-pressure">
                                        <span class="pressure-value">12.8</span>
                                        <span class="pressure-unit">Pa</span>
                                    </div>
                                </div>
                                <div class="pressure-status status-warning">偏低</div>
                            </div>
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">缓冲区域</div>
                                    <div class="zone-pressure">
                                        <span class="pressure-value">8.5</span>
                                        <span class="pressure-unit">Pa</span>
                                    </div>
                                </div>
                                <div class="pressure-status status-normal">正常</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 压差控制 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-sliders-h"></i>
                                压差控制
                            </h3>
                        </div>
                        <div class="control-panel">
                            <div class="control-item">
                                <label class="form-label">目标压差 (Pa)</label>
                                <input type="number" class="form-input" value="15" min="5" max="30" step="0.1">
                            </div>
                            <div class="control-item">
                                <label class="form-label">控制模式</label>
                                <select class="form-select">
                                    <option value="auto">自动控制</option>
                                    <option value="manual">手动控制</option>
                                </select>
                            </div>
                            <div class="control-actions">
                                <button class="btn btn-primary">应用设置</button>
                                <button class="btn btn-secondary">重置</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 压差梯度图 -->
                <div class="card mt-6">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-area"></i>
                            压差梯度分布
                        </h3>
                    </div>
                    <div class="gradient-visualization">
                        <div class="room-layout">
                            <div class="room-zone high-pressure">
                                <div class="zone-label">生产区域A</div>
                                <div class="zone-value">15.2 Pa</div>
                            </div>
                            <div class="room-zone medium-pressure">
                                <div class="zone-label">生产区域B</div>
                                <div class="zone-value">12.8 Pa</div>
                            </div>
                            <div class="room-zone low-pressure">
                                <div class="zone-label">缓冲区域</div>
                                <div class="zone-value">8.5 Pa</div>
                            </div>
                            <div class="room-zone lowest-pressure">
                                <div class="zone-label">更衣区域</div>
                                <div class="zone-value">5.2 Pa</div>
                            </div>
                        </div>
                        <div class="pressure-legend">
                            <div class="legend-item">
                                <div class="legend-color high"></div>
                                <span>高压区 (>12 Pa)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color medium"></div>
                                <span>中压区 (8-12 Pa)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color low"></div>
                                <span>低压区 (<8 Pa)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <style>
        .pressure-container {
            padding: var(--spacing-lg);
        }
        
        .pressure-zones {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .zone-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .zone-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .zone-pressure {
            display: flex;
            align-items: baseline;
            gap: var(--spacing-xs);
        }
        
        .pressure-value {
            font-family: var(--font-mono);
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .pressure-unit {
            font-size: 0.875rem;
            color: var(--text-muted);
        }
        
        .pressure-status {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .pressure-status.status-normal {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
        }
        
        .pressure-status.status-warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .control-panel {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .control-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .control-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        
        .gradient-visualization {
            padding: var(--spacing-lg);
        }
        
        .room-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            min-height: 300px;
        }
        
        .room-zone {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .room-zone.high-pressure {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
            border: 2px solid rgba(239, 68, 68, 0.3);
        }
        
        .room-zone.medium-pressure {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(245, 158, 11, 0.1));
            border: 2px solid rgba(245, 158, 11, 0.3);
        }
        
        .room-zone.low-pressure {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
            border: 2px solid rgba(59, 130, 246, 0.3);
        }
        
        .room-zone.lowest-pressure {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
            border: 2px solid rgba(16, 185, 129, 0.3);
        }
        
        .zone-label {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .zone-value {
            font-family: var(--font-mono);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .pressure-legend {
            display: flex;
            justify-content: center;
            gap: var(--spacing-lg);
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: var(--radius-sm);
        }
        
        .legend-color.high {
            background: rgba(239, 68, 68, 0.6);
        }
        
        .legend-color.medium {
            background: rgba(245, 158, 11, 0.6);
        }
        
        .legend-color.low {
            background: rgba(59, 130, 246, 0.6);
        }
    </style>
    
    <!-- 脚本文件 -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const pageTitle = document.getElementById('pageTitle');
                const breadcrumb = document.getElementById('breadcrumb');
                
                if (pageTitle) {
                    pageTitle.textContent = '压差管理';
                }
                
                if (breadcrumb) {
                    breadcrumb.innerHTML = `
                        <span class="breadcrumb-item">首页</span>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                        <span class="breadcrumb-item active">压差管理</span>
                    `;
                }
            }, 500);
        });
    </script>
</body>
</html>
