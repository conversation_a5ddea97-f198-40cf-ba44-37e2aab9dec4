// 洁净空调系统 - 设备管理页面脚本

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待组件加载完成
    setTimeout(() => {
        initEquipment();
    }, 500);
});

// 初始化设备管理页面
function initEquipment() {
    // 设置页面标题
    updatePageTitle('设备管理', ['首页', '设备管理']);
    
    // 初始化事件监听
    initEventListeners();
    
    // 加载初始数据
    loadEquipmentData();
    
    // 监听数据刷新事件
    document.addEventListener('dataRefresh', handleDataRefresh);
    
    // 启动实时数据更新
    startRealTimeUpdates();
}

// 更新页面标题和面包屑
function updatePageTitle(title, breadcrumbs) {
    const pageTitle = document.getElementById('pageTitle');
    const breadcrumb = document.getElementById('breadcrumb');
    
    if (pageTitle) {
        pageTitle.textContent = title;
    }
    
    if (breadcrumb && breadcrumbs) {
        breadcrumb.innerHTML = breadcrumbs.map((item, index) => {
            const isLast = index === breadcrumbs.length - 1;
            return `
                <span class="breadcrumb-item ${isLast ? 'active' : ''}">${item}</span>
                ${!isLast ? '<i class="fas fa-chevron-right breadcrumb-separator"></i>' : ''}
            `;
        }).join('');
    }
}

// 初始化事件监听
function initEventListeners() {
    // 导出FFU报告按钮
    const exportFFUBtn = document.getElementById('exportFFUBtn');
    if (exportFFUBtn) {
        exportFFUBtn.addEventListener('click', exportFFUReport);
    }
    
    // 添加FFU设备按钮
    const addFFUBtn = document.getElementById('addFFUBtn');
    if (addFFUBtn) {
        addFFUBtn.addEventListener('click', showAddFFUModal);
    }
    
    // 添加库存按钮
    const addInventoryBtn = document.getElementById('addInventoryBtn');
    if (addInventoryBtn) {
        addInventoryBtn.addEventListener('click', showAddInventoryModal);
    }
    
    // FFU卡片操作按钮
    initFFUCardActions();
    
    // 库存表格操作按钮
    initInventoryTableActions();
}

// 初始化FFU卡片操作
function initFFUCardActions() {
    const ffuCards = document.querySelectorAll('.ffu-card');
    ffuCards.forEach(card => {
        const actions = card.querySelectorAll('.ffu-actions .btn');
        actions.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const ffuName = card.querySelector('.ffu-name').textContent;
                const action = this.textContent.trim();
                handleFFUAction(ffuName, action);
            });
        });
    });
}

// 初始化库存表格操作
function initInventoryTableActions() {
    const tableActions = document.querySelectorAll('.table-actions .btn');
    tableActions.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const row = this.closest('tr');
            const modelName = row.querySelector('.model-name').textContent;
            const action = this.getAttribute('title');
            handleInventoryAction(modelName, action);
        });
    });
}

// 加载设备数据
function loadEquipmentData() {
    // 模拟异步数据加载
    setTimeout(() => {
        updateFFUData();
        updateInventoryData();
    }, 1000);
}

// 更新FFU数据
function updateFFUData() {
    const ffuCards = document.querySelectorAll('.ffu-card');
    ffuCards.forEach((card, index) => {
        // 模拟实时数据更新
        const metrics = card.querySelectorAll('.metric-value');
        if (metrics.length >= 4) {
            // 风机转速
            const baseRPM = 1400 + index * 50;
            const currentRPM = baseRPM + Math.floor((Math.random() - 0.5) * 100);
            metrics[0].innerHTML = `${currentRPM.toLocaleString()} <span class="unit">RPM</span>`;
            
            // 压差
            const basePressure = 180 + index * 20;
            const currentPressure = basePressure + Math.floor((Math.random() - 0.5) * 50);
            metrics[1].innerHTML = `${currentPressure} <span class="unit">Pa</span>`;
            
            // 功率
            const basePower = 85 + index * 5;
            const currentPower = basePower + Math.floor((Math.random() - 0.5) * 10);
            metrics[2].innerHTML = `${currentPower} <span class="unit">W</span>`;
            
            // 运行时间（每小时增加）
            const baseHours = 2000 + index * 500;
            const currentHours = baseHours + Math.floor(Date.now() / 3600000) % 24;
            metrics[3].innerHTML = `${currentHours.toLocaleString()} <span class="unit">h</span>`;
        }
        
        // 更新过滤器状态
        updateFilterStatus(card, index);
    });
}

// 更新过滤器状态
function updateFilterStatus(card, index) {
    const filterProgress = card.querySelector('.progress-fill');
    const filterHealth = card.querySelector('.filter-health');
    const filterRemaining = card.querySelector('.filter-remaining');
    
    if (filterProgress && filterHealth && filterRemaining) {
        // 模拟过滤器寿命递减
        const baseLife = [85, 15, 92][index] || 80;
        const currentLife = Math.max(0, baseLife - Math.floor(Date.now() / 86400000) % 5);
        
        filterProgress.style.width = `${currentLife}%`;
        filterRemaining.textContent = `剩余寿命: ${currentLife}%`;
        
        // 更新健康状态
        if (currentLife > 50) {
            filterHealth.className = 'filter-health good';
            filterHealth.textContent = '良好';
            filterProgress.className = 'progress-fill';
        } else if (currentLife > 20) {
            filterHealth.className = 'filter-health warning';
            filterHealth.textContent = '需关注';
            filterProgress.className = 'progress-fill warning';
        } else {
            filterHealth.className = 'filter-health danger';
            filterHealth.textContent = '需更换';
            filterProgress.className = 'progress-fill danger';
        }
    }
}

// 更新库存数据
function updateInventoryData() {
    const inventoryCounts = document.querySelectorAll('.inventory-count');
    inventoryCounts.forEach((count, index) => {
        // 模拟库存变化
        const baseCounts = [25, 8, 45];
        const currentCount = Math.max(0, baseCounts[index] + Math.floor((Math.random() - 0.5) * 5));
        count.textContent = currentCount;
        
        // 更新状态指示器
        const statusIndicator = count.closest('tr').querySelector('.status-indicator');
        const safetyStock = [10, 15, 20][index];
        
        if (currentCount > safetyStock) {
            statusIndicator.className = 'status-indicator status-success';
            statusIndicator.innerHTML = '<i class="fas fa-circle"></i>充足';
        } else if (currentCount > safetyStock * 0.5) {
            statusIndicator.className = 'status-indicator status-warning';
            statusIndicator.innerHTML = '<i class="fas fa-exclamation-triangle"></i>不足';
        } else {
            statusIndicator.className = 'status-indicator status-danger';
            statusIndicator.innerHTML = '<i class="fas fa-times-circle"></i>紧急';
        }
    });
}

// 处理FFU操作
function handleFFUAction(ffuName, action) {
    console.log(`FFU操作: ${ffuName} - ${action}`);
    
    switch (action) {
        case '详细数据':
            showFFUDetails(ffuName);
            break;
        case '维护记录':
            showMaintenanceRecords(ffuName);
            break;
        case '立即维护':
            scheduleMaintenance(ffuName);
            break;
        case '设置':
            showFFUSettings(ffuName);
            break;
        default:
            console.log('未知操作:', action);
    }
}

// 显示FFU详细数据
function showFFUDetails(ffuName) {
    showNotification(`正在加载 ${ffuName} 的详细数据...`, 'info');
    // 这里可以实现详细数据模态框或跳转到详细页面
}

// 显示维护记录
function showMaintenanceRecords(ffuName) {
    showNotification(`正在加载 ${ffuName} 的维护记录...`, 'info');
    // 这里可以实现维护记录模态框
}

// 安排维护
function scheduleMaintenance(ffuName) {
    if (confirm(`确定要为 ${ffuName} 安排维护吗？`)) {
        showNotification(`已为 ${ffuName} 安排维护任务`, 'success');
        // 这里可以实现维护任务创建逻辑
    }
}

// 显示FFU设置
function showFFUSettings(ffuName) {
    showNotification(`正在打开 ${ffuName} 的设置...`, 'info');
    // 这里可以实现设置模态框
}

// 处理库存操作
function handleInventoryAction(modelName, action) {
    console.log(`库存操作: ${modelName} - ${action}`);
    
    switch (action) {
        case '编辑':
            editInventoryItem(modelName);
            break;
        case '入库':
            showStockInModal(modelName);
            break;
        case '出库':
            showStockOutModal(modelName);
            break;
        default:
            console.log('未知操作:', action);
    }
}

// 编辑库存项目
function editInventoryItem(modelName) {
    showNotification(`正在编辑 ${modelName}...`, 'info');
    // 这里可以实现编辑模态框
}

// 显示入库模态框
function showStockInModal(modelName) {
    const quantity = prompt(`请输入 ${modelName} 的入库数量:`);
    if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
        showNotification(`${modelName} 入库 ${quantity} 个`, 'success');
        // 这里可以实现实际的入库逻辑
        setTimeout(() => updateInventoryData(), 1000);
    }
}

// 显示出库模态框
function showStockOutModal(modelName) {
    const quantity = prompt(`请输入 ${modelName} 的出库数量:`);
    if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
        showNotification(`${modelName} 出库 ${quantity} 个`, 'success');
        // 这里可以实现实际的出库逻辑
        setTimeout(() => updateInventoryData(), 1000);
    }
}

// 导出FFU报告
function exportFFUReport() {
    // 收集FFU数据
    const ffuData = [];
    const ffuCards = document.querySelectorAll('.ffu-card');
    
    ffuCards.forEach(card => {
        const name = card.querySelector('.ffu-name').textContent;
        const location = card.querySelector('.ffu-location').textContent;
        const status = card.querySelector('.ffu-status .status-indicator').textContent.trim();
        const metrics = Array.from(card.querySelectorAll('.metric-value')).map(m => m.textContent);
        const filterHealth = card.querySelector('.filter-health').textContent;
        const filterLife = card.querySelector('.filter-remaining').textContent;
        
        ffuData.push({
            name,
            location,
            status,
            rpm: metrics[0] || '',
            pressure: metrics[1] || '',
            power: metrics[2] || '',
            runtime: metrics[3] || '',
            filterHealth,
            filterLife
        });
    });
    
    // 创建CSV内容
    const csvContent = [
        ['设备名称', '位置', '状态', '转速', '压差', '功率', '运行时间', '过滤器状态', '剩余寿命'],
        ...ffuData.map(item => [
            item.name,
            item.location,
            item.status,
            item.rpm,
            item.pressure,
            item.power,
            item.runtime,
            item.filterHealth,
            item.filterLife
        ])
    ].map(row => row.join(',')).join('\n');
    
    // 下载文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `FFU设备报告_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showNotification('FFU设备报告导出成功', 'success');
}

// 显示添加FFU模态框
function showAddFFUModal() {
    showNotification('添加FFU设备功能开发中', 'info');
    // 这里可以实现添加FFU设备的模态框
}

// 显示添加库存模态框
function showAddInventoryModal() {
    showNotification('添加库存功能开发中', 'info');
    // 这里可以实现添加库存的模态框
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 
                           type === 'warning' ? 'exclamation-triangle' : 
                           type === 'error' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    const colors = {
        success: 'var(--success-color)',
        warning: 'var(--warning-color)',
        error: 'var(--danger-color)',
        info: 'var(--info-color)'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// 启动实时数据更新
function startRealTimeUpdates() {
    // 每10秒更新一次FFU数据
    setInterval(() => {
        updateFFUData();
    }, 10000);
    
    // 每30秒更新一次库存数据
    setInterval(() => {
        updateInventoryData();
    }, 30000);
}

// 处理数据刷新事件
function handleDataRefresh(event) {
    console.log('刷新设备管理数据', event.detail);
    updateFFUData();
    updateInventoryData();
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
