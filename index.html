<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - 系统概览</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                <h1>系统概览</h1>
                <p class="page-description">洁净空调系统实时监控与管理</p>
            </div>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 关键指标卡片 -->
            <section class="metrics-section">
                <div class="grid grid-cols-4 gap-6 mb-6">
                    <!-- 温度监控 -->
                    <div class="card metric-card temperature-card">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <div class="metric-status">正常</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">22.5<span class="metric-unit">°C</span></div>
                            <div class="metric-label">温度监控</div>
                            <div class="metric-range">范围: 18-25°C</div>
                        </div>
                        <div class="metric-chart">
                            <div class="mini-trend"></div>
                        </div>
                    </div>
                    
                    <!-- 湿度监控 -->
                    <div class="card metric-card humidity-card">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="metric-status">正常</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">45<span class="metric-unit">%</span></div>
                            <div class="metric-label">湿度监控</div>
                            <div class="metric-range">范围: 40-60%</div>
                        </div>
                        <div class="metric-chart">
                            <div class="mini-trend"></div>
                        </div>
                    </div>
                    
                    <!-- 设备状态 -->
                    <div class="card metric-card status-card">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="metric-status">正常</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">正常<span class="metric-unit"></span></div>
                            <div class="metric-label">设备状态</div>
                            <div class="metric-range">12台设备在线</div>
                        </div>
                        <div class="metric-chart">
                            <div class="mini-trend"></div>
                        </div>
                    </div>
                    
                    <!-- 洁净监控 -->
                    <div class="card metric-card cleanliness-card">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-microscope"></i>
                            </div>
                            <div class="metric-status">正常</div>
                        </div>
                        <div class="metric-content">
                            <div class="metric-value">正常<span class="metric-unit"></span></div>
                            <div class="metric-label">洁净监控</div>
                            <div class="metric-range">ISO Class 5</div>
                        </div>
                        <div class="metric-chart">
                            <div class="mini-trend"></div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 实时监控图表 -->
            <section class="charts-section">
                <div class="grid grid-cols-2 gap-6 mb-6">
                    <!-- 环境参数趋势 -->
                    <div class="card chart-card">
                        <div class="card-header">
                            <h3 class="card-title">环境参数趋势</h3>
                            <div class="chart-controls">
                                <select class="form-select" id="timeRange">
                                    <option value="1h">最近1小时</option>
                                    <option value="6h">最近6小时</option>
                                    <option value="24h" selected>最近24小时</option>
                                    <option value="7d">最近7天</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="environmentChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 设备运行状态 -->
                    <div class="card chart-card">
                        <div class="card-header">
                            <h3 class="card-title">设备运行状态</h3>
                            <div class="legend">
                                <span class="legend-item">
                                    <span class="legend-color" style="background: var(--success-color);"></span>
                                    正常 (85%)
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: var(--warning-color);"></span>
                                    警告 (12%)
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: var(--danger-color);"></span>
                                    故障 (3%)
                                </span>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="equipmentChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- 系统概览 -->
            <section class="overview-section">
                <div class="grid grid-cols-3 gap-6">
                    <!-- 区域状态 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">区域状态监控</h3>
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-expand"></i>
                                详细视图
                            </button>
                        </div>
                        <div class="zone-list">
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">生产区域A</div>
                                    <div class="zone-details">ISO Class 5 | 23.2°C | 51%RH</div>
                                </div>
                                <div class="zone-status">
                                    <span class="status-indicator status-success">
                                        <i class="fas fa-circle"></i>
                                        正常
                                    </span>
                                </div>
                            </div>
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">生产区域B</div>
                                    <div class="zone-details">ISO Class 6 | 24.1°C | 48%RH</div>
                                </div>
                                <div class="zone-status">
                                    <span class="status-indicator status-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        警告
                                    </span>
                                </div>
                            </div>
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">缓冲区域</div>
                                    <div class="zone-details">ISO Class 7 | 22.8°C | 53%RH</div>
                                </div>
                                <div class="zone-status">
                                    <span class="status-indicator status-success">
                                        <i class="fas fa-circle"></i>
                                        正常
                                    </span>
                                </div>
                            </div>
                            <div class="zone-item">
                                <div class="zone-info">
                                    <div class="zone-name">更衣区域</div>
                                    <div class="zone-details">ISO Class 8 | 23.5°C | 50%RH</div>
                                </div>
                                <div class="zone-status">
                                    <span class="status-indicator status-success">
                                        <i class="fas fa-circle"></i>
                                        正常
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 报警信息 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">实时报警</h3>
                            <button class="btn btn-sm btn-secondary">
                                <i class="fas fa-bell"></i>
                                全部报警
                            </button>
                        </div>
                        <div class="alert-list">
                            <div class="alert-item warning">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">过滤器压差异常</div>
                                    <div class="alert-desc">FFU-001 压差超过阈值 250Pa</div>
                                    <div class="alert-time">2分钟前</div>
                                </div>
                                <div class="alert-actions">
                                    <button class="btn btn-sm btn-warning">处理</button>
                                </div>
                            </div>
                            <div class="alert-item info">
                                <div class="alert-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">维护提醒</div>
                                    <div class="alert-desc">区域A高效过滤器需要更换</div>
                                    <div class="alert-time">1小时前</div>
                                </div>
                                <div class="alert-actions">
                                    <button class="btn btn-sm btn-secondary">查看</button>
                                </div>
                            </div>
                            <div class="alert-item success">
                                <div class="alert-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">系统优化完成</div>
                                    <div class="alert-desc">自动控制策略已更新</div>
                                    <div class="alert-time">3小时前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 能效统计 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">能效统计</h3>
                            <div class="period-selector">
                                <button class="period-btn active">今日</button>
                                <button class="period-btn">本周</button>
                                <button class="period-btn">本月</button>
                            </div>
                        </div>
                        <div class="energy-stats">
                            <div class="energy-item">
                                <div class="energy-label">总功耗</div>
                                <div class="energy-value">
                                    <span class="value">1,245</span>
                                    <span class="unit">kWh</span>
                                </div>
                                <div class="energy-change positive">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>-8.5%</span>
                                </div>
                            </div>
                            <div class="energy-item">
                                <div class="energy-label">风机功耗</div>
                                <div class="energy-value">
                                    <span class="value">856</span>
                                    <span class="unit">kWh</span>
                                </div>
                                <div class="energy-change positive">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>-5.2%</span>
                                </div>
                            </div>
                            <div class="energy-item">
                                <div class="energy-label">制冷功耗</div>
                                <div class="energy-value">
                                    <span class="value">389</span>
                                    <span class="unit">kWh</span>
                                </div>
                                <div class="energy-change negative">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+2.1%</span>
                                </div>
                            </div>
                            <div class="energy-efficiency">
                                <div class="efficiency-label">能效比 (EER)</div>
                                <div class="efficiency-value">3.2</div>
                                <div class="efficiency-bar">
                                    <div class="efficiency-fill" style="width: 80%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>

    <!-- 首页样式 -->
    <style>


        .dashboard-container {
            padding: var(--spacing-lg);
        }

        .metrics-section .grid {
            margin-bottom: var(--spacing-xl);
        }

        .metric-card {
            position: relative;
            overflow: hidden;
            border-left: 4px solid transparent;
        }

        /* 温度卡片 - 橙红色 */
        .temperature-card {
            border-left-color: #ff6b35;
        }

        .temperature-card .metric-icon {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
        }

        /* 湿度卡片 - 蓝色 */
        .humidity-card {
            border-left-color: #4a90e2;
        }

        .humidity-card .metric-icon {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
        }

        /* 设备状态卡片 - 粉色 */
        .status-card {
            border-left-color: #e91e63;
        }

        .status-card .metric-icon {
            background: linear-gradient(135deg, #e91e63, #c2185b);
            color: white;
        }

        /* 洁净度卡片 - 青色 */
        .cleanliness-card {
            border-left-color: #00bcd4;
        }

        .cleanliness-card .metric-icon {
            background: linear-gradient(135deg, #00bcd4, #0097a7);
            color: white;
        }

        .metric-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--spacing-md);
        }

        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .metric-status {
            font-size: 0.875rem;
            color: var(--success-color);
            font-weight: 500;
        }

        .metric-range {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: var(--spacing-xs);
        }

        .metric-chart {
            margin-top: var(--spacing-md);
            height: 40px;
            position: relative;
        }

        .mini-trend {
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(74, 144, 226, 0.1) 0%,
                rgba(74, 144, 226, 0.2) 50%,
                rgba(74, 144, 226, 0.1) 100%);
            border-radius: var(--radius-sm);
            position: relative;
            overflow: hidden;
        }

        .mini-trend::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(74, 144, 226, 0.8) 20%,
                rgba(74, 144, 226, 1) 50%,
                rgba(74, 144, 226, 0.8) 80%,
                transparent 100%);
            transform: translateY(-50%);
        }

        .metric-content {
            text-align: left;
        }

        .metric-value {
            font-family: var(--font-mono);
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .metric-unit {
            font-size: 1rem;
            font-weight: 400;
            color: var(--text-muted);
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .metric-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .metric-trend.positive {
            color: var(--success-color);
        }

        .metric-trend.negative {
            color: var(--danger-color);
        }

        .metric-trend.stable {
            color: var(--text-muted);
        }

        .chart-card {
            min-height: 400px;
        }

        .chart-controls {
            display: flex;
            gap: var(--spacing-sm);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: var(--spacing-md);
        }

        .legend {
            display: flex;
            gap: var(--spacing-md);
            font-size: 0.875rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .zone-list, .alert-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .zone-item, .alert-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }

        .zone-info, .alert-content {
            flex: 1;
            min-width: 0;
        }

        .zone-name, .alert-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .zone-details, .alert-desc {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .alert-time {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .alert-item {
            border-left: 4px solid transparent;
        }

        .alert-item.warning {
            border-left-color: var(--warning-color);
        }

        .alert-item.info {
            border-left-color: var(--info-color);
        }

        .alert-item.success {
            border-left-color: var(--success-color);
        }

        .alert-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--spacing-sm);
        }

        .alert-item.warning .alert-icon {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .alert-item.info .alert-icon {
            background: rgba(14, 165, 233, 0.1);
            color: var(--info-color);
        }

        .alert-item.success .alert-icon {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
        }

        .period-selector {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            padding: 2px;
        }

        .period-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 0.75rem;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: var(--transition);
        }

        .period-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .energy-stats {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .energy-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }

        .energy-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .energy-value {
            display: flex;
            align-items: baseline;
            gap: var(--spacing-xs);
        }

        .energy-value .value {
            font-family: var(--font-mono);
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .energy-value .unit {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .energy-change {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .energy-change.positive {
            color: var(--success-color);
        }

        .energy-change.negative {
            color: var(--danger-color);
        }

        .energy-efficiency {
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--border-color);
        }

        .efficiency-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }

        .efficiency-value {
            font-family: var(--font-mono);
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .efficiency-bar {
            height: 6px;
            background: var(--bg-tertiary);
            border-radius: 3px;
            overflow: hidden;
        }

        .efficiency-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), var(--primary-color));
            border-radius: 3px;
            transition: width 0.5s ease;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .grid-cols-4 {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }

            .grid-cols-3 {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: var(--spacing-md);
            }

            .grid-cols-2 {
                grid-template-columns: repeat(1, minmax(0, 1fr));
            }

            .metric-value {
                font-size: 2rem;
            }

            .chart-container {
                height: 250px;
            }
        }
    </style>

    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="assets/js/common.js"></script>
    <script src="assets/js/dashboard.js"></script>
</body>
</html>
