# 洁净空调系统 - 前端原型界面

## 项目概述

这是一个完整的洁净空调系统前端原型界面，采用现代化的Web设计规范，提供了丰富的功能模块和优秀的用户体验。系统专为洁净室环境设计，支持实时监控、设备管理、自动控制等核心功能。

## 功能特性

### 🏠 系统概览
- 实时关键指标展示
- 系统状态监控
- 环境参数趋势图
- 设备运行状态统计
- 区域状态监控
- 实时报警信息
- 能效统计分析

### 📊 环境监测
- 多源传感器数据展示（温度、湿度、压差、风速、颗粒物、CO₂）
- 实时数据更新（5秒频率）
- 历史趋势分析
- 报警阈值设置
- 自动控制策略联动
- 数据导出功能

### ⚙️ 设备管理
- FFU设备状态监控
- 过滤器寿命管理
- 设备运行参数显示
- 维护记录管理
- 过滤器库存管理
- 设备性能分析

### 🌪️ 风量控制
- 实时风量监控
- 自动/手动控制模式
- ACH（换气次数）计算器
- 气流模式设置（层流/湍流/混合）
- 风量调节控制
- 送风方式配置

### 📈 压差管理
- 实时压差监测
- 压差梯度可视化
- 正/负压区域管理
- 压差控制设置
- 趋势分析功能

### 🤖 自动控制
- 自动控制策略管理
- 能效优化功能
- 定时任务管理
- 节能模式控制
- DCV（需求控制通风）

### 📋 合规管理
- ISO 14644标准管理
- 洁净度验证工具
- 合规报告生成
- 过滤器完整性测试
- 颗粒计数校验

### 🔧 维护管理
- 工单管理系统
- 保养计划安排
- 维护记录追踪
- 任务绩效统计
- 成本分析

### 📺 大屏展示
- 控制室大屏仪表盘
- 全屏显示模式
- 实时数据更新
- 大字体显示
- 适配大屏分辨率

### 🧠 AI模型
- CFD空气流场模拟
- AI预测性维护
- 模型训练管理
- 故障预警系统
- 智能优化建议

## 技术特点

### 🎨 现代化UI设计
- 明亮清新的主题，提供优秀的视觉体验
- 响应式设计，支持多设备
- 高对比度，确保可读性
- 统一的设计语言
- 丰富的图标和动画效果
- 完整的页脚信息，包含公司信息和服务链接

### 📱 移动端友好
- 响应式布局
- 触摸友好的交互
- 移动端优化的导航
- 适配不同屏幕尺寸

### ⚡ 高性能
- 模块化代码结构
- 异步数据加载
- 图表性能优化
- 内存管理优化

### 🔧 可扩展性
- 组件化架构
- 统一的样式系统
- 可配置的主题
- 插件化功能模块

## 文件结构

```
洁净空调系统/
├── index.html                 # 主入口页面
├── monitoring.html            # 环境监测页面
├── equipment.html             # 设备管理页面
├── airflow.html              # 风量控制页面
├── pressure.html             # 压差管理页面
├── automation.html           # 自动控制页面
├── compliance.html           # 合规管理页面
├── maintenance.html          # 维护管理页面
├── dashboard-large.html      # 大屏展示页面
├── ai-models.html            # AI模型页面
├── assets/
│   ├── css/
│   │   └── common.css        # 公共样式文件
│   └── js/
│       ├── common.js         # 公共JavaScript文件
│       ├── dashboard.js      # 首页脚本
│       ├── monitoring.js     # 环境监测脚本
│       ├── equipment.js      # 设备管理脚本
│       └── dashboard-large.js # 大屏展示脚本
├── components/
│   ├── navigation.html       # 导航栏组件
│   ├── header.html          # 页面头部组件
│   └── footer.html          # 页面页脚组件
└── README.md                # 项目说明文档
```

## 使用说明

### 快速开始

1. **直接打开**：使用现代浏览器直接打开 `index.html` 文件
2. **本地服务器**：推荐使用本地HTTP服务器运行，避免跨域问题

```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 使用Node.js http-server
npx http-server

# 使用Live Server (VS Code扩展)
# 右键点击index.html -> Open with Live Server
```

3. **访问地址**：在浏览器中访问 `http://localhost:8000`

### 页面导航

- **系统概览**：`index.html` - 系统总览和关键指标
- **环境监测**：`monitoring.html` - 传感器数据和环境控制
- **设备管理**：`equipment.html` - FFU设备和过滤器管理
- **风量控制**：`airflow.html` - 风量调节和气流控制
- **压差管理**：`pressure.html` - 压差监测和控制
- **自动控制**：`automation.html` - 自动化策略和节能控制
- **合规管理**：`compliance.html` - ISO标准和验证工具
- **维护管理**：`maintenance.html` - 工单和保养管理
- **大屏展示**：`dashboard-large.html` - 控制室大屏显示
- **AI模型**：`ai-models.html` - AI分析和预测功能

### 功能特色

#### 实时数据更新
- 自动刷新间隔：30秒
- 传感器数据更新：5秒
- 图表动态更新
- 状态实时同步

#### 交互功能
- 响应式导航栏
- 可折叠侧边栏
- 实时通知系统
- 模态框和下拉菜单
- 数据导出功能

#### 可视化图表
- Chart.js图表库
- 实时趋势图
- 设备状态饼图
- 压差梯度可视化
- CFD流场模拟

## 浏览器兼容性

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ⚠️ IE 11（部分功能受限）

## 开发说明

### 技术栈
- **HTML5**：语义化标签，现代Web标准
- **CSS3**：Flexbox、Grid、CSS变量、动画
- **JavaScript ES6+**：模块化、异步编程、现代语法
- **Chart.js**：图表可视化库
- **Font Awesome**：图标库

### 设计规范
- **色彩系统**：明亮清新主题，高对比度，优秀的视觉体验
- **字体系统**：Inter主字体，JetBrains Mono等宽字体
- **间距系统**：8px基础单位，统一间距规范
- **组件系统**：可复用的UI组件，包含完整的页脚信息

### 自定义配置

#### 主题颜色
在 `assets/css/common.css` 中修改CSS变量：

```css
:root {
  --primary-color: #1e40af;    /* 主色调 */
  --success-color: #059669;    /* 成功色 */
  --warning-color: #f59e0b;    /* 警告色 */
  --danger-color: #dc2626;     /* 危险色 */
  /* ... 更多颜色变量 */
}
```

#### 数据更新频率
在相应的JavaScript文件中修改：

```javascript
// 自动刷新间隔（毫秒）
const AUTO_REFRESH_INTERVAL = 30000; // 30秒

// 传感器数据更新间隔
const SENSOR_UPDATE_INTERVAL = 5000; // 5秒
```

## 部署建议

### 生产环境
1. **Web服务器**：Nginx、Apache或IIS
2. **HTTPS**：启用SSL证书
3. **压缩**：启用Gzip压缩
4. **缓存**：设置适当的缓存策略
5. **CDN**：使用CDN加速静态资源

### 集成建议
1. **后端API**：替换模拟数据为真实API调用
2. **WebSocket**：实现真正的实时数据推送
3. **用户认证**：集成用户登录和权限管理
4. **数据库**：连接实际的数据存储系统

## 许可证

本项目仅供学习和演示使用。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**：本项目为前端原型界面，包含模拟数据。在实际部署时需要集成真实的后端服务和数据源。
