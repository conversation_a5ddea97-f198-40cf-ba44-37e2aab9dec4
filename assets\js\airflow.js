// 洁净空调系统 - 风量控制页面脚本

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待组件加载完成
    setTimeout(() => {
        initAirflow();
    }, 500);
});

// 初始化风量控制页面
function initAirflow() {
    // 设置页面标题
    updatePageTitle('风量控制', ['首页', '风量控制']);
    
    // 初始化事件监听
    initEventListeners();
    
    // 加载初始数据
    loadAirflowData();
    
    // 监听数据刷新事件
    document.addEventListener('dataRefresh', handleDataRefresh);
    
    // 启动实时数据更新
    startRealTimeUpdates();
}

// 更新页面标题和面包屑
function updatePageTitle(title, breadcrumbs) {
    const pageTitle = document.getElementById('pageTitle');
    const breadcrumb = document.getElementById('breadcrumb');
    
    if (pageTitle) {
        pageTitle.textContent = title;
    }
    
    if (breadcrumb && breadcrumbs) {
        breadcrumb.innerHTML = breadcrumbs.map((item, index) => {
            const isLast = index === breadcrumbs.length - 1;
            return `
                <span class="breadcrumb-item ${isLast ? 'active' : ''}">${item}</span>
                ${!isLast ? '<i class="fas fa-chevron-right breadcrumb-separator"></i>' : ''}
            `;
        }).join('');
    }
}

// 初始化事件监听
function initEventListeners() {
    // 控制模式选择
    const controlMode = document.getElementById('controlMode');
    if (controlMode) {
        controlMode.addEventListener('change', function() {
            handleControlModeChange(this.value);
        });
    }
    
    // 风量滑块控制
    const sliders = document.querySelectorAll('.airflow-slider');
    sliders.forEach(slider => {
        slider.addEventListener('input', function() {
            updateSliderValue(this);
        });
        
        slider.addEventListener('change', function() {
            applyAirflowChange(this);
        });
    });
    
    // 控制按钮
    initControlButtons();
    
    // ACH计算器
    const calculateBtn = document.getElementById('calculateACH');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateACH);
    }
    
    // 气流模式选择
    const patternBtns = document.querySelectorAll('.pattern-btn');
    patternBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            selectAirflowPattern(this);
        });
    });
}

// 初始化控制按钮
function initControlButtons() {
    // 减少按钮
    const decreaseBtns = document.querySelectorAll('[id^="decrease"]');
    decreaseBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const zone = this.id.replace('decrease', '');
            adjustAirflow(zone, -5);
        });
    });
    
    // 增加按钮
    const increaseBtns = document.querySelectorAll('[id^="increase"]');
    increaseBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const zone = this.id.replace('increase', '');
            adjustAirflow(zone, 5);
        });
    });
    
    // 自动按钮
    const autoBtns = document.querySelectorAll('[id^="auto"]');
    autoBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const zone = this.id.replace('auto', '');
            enableAutoControl(zone);
        });
    });
}

// 加载风量数据
function loadAirflowData() {
    // 模拟异步数据加载
    setTimeout(() => {
        updateAirflowMetrics();
        updateSliderValues();
    }, 1000);
}

// 更新风量指标
function updateAirflowMetrics() {
    // 模拟实时数据更新
    const zones = [
        { id: 'A', currentFlow: 2850, targetFlow: 3000, ach: 15.2, frequency: 45.8 },
        { id: 'B', currentFlow: 2150, targetFlow: 2500, ach: 12.8, frequency: 38.2 }
    ];
    
    zones.forEach(zone => {
        // 添加随机波动
        const currentFlow = zone.currentFlow + Math.floor((Math.random() - 0.5) * 100);
        const ach = zone.ach + (Math.random() - 0.5) * 0.5;
        const frequency = zone.frequency + (Math.random() - 0.5) * 2;
        
        // 更新显示值（这里需要根据实际HTML结构调整选择器）
        updateZoneMetric(zone.id, 'currentFlow', currentFlow, 'm³/h');
        updateZoneMetric(zone.id, 'ach', ach.toFixed(1), '次/h');
        updateZoneMetric(zone.id, 'frequency', frequency.toFixed(1), 'Hz');
    });
}

// 更新区域指标
function updateZoneMetric(zoneId, metricType, value, unit) {
    // 这里需要根据实际的HTML结构来更新对应的元素
    console.log(`更新区域${zoneId}的${metricType}: ${value}${unit}`);
}

// 更新滑块值
function updateSliderValues() {
    const sliders = document.querySelectorAll('.airflow-slider');
    sliders.forEach(slider => {
        updateSliderValue(slider);
    });
}

// 更新单个滑块值显示
function updateSliderValue(slider) {
    const value = slider.value;
    const valueDisplay = slider.parentElement.querySelector('.slider-value');
    if (valueDisplay) {
        valueDisplay.textContent = `${value}%`;
    }
}

// 处理控制模式变化
function handleControlModeChange(mode) {
    console.log('控制模式切换到:', mode);
    
    const sliders = document.querySelectorAll('.airflow-slider');
    const controlButtons = document.querySelectorAll('.control-buttons .btn');
    
    if (mode === 'manual') {
        // 手动模式：启用所有控制
        sliders.forEach(slider => slider.disabled = false);
        controlButtons.forEach(btn => btn.disabled = false);
        showNotification('已切换到手动控制模式', 'info');
    } else if (mode === 'auto') {
        // 自动模式：禁用手动控制
        sliders.forEach(slider => slider.disabled = true);
        controlButtons.forEach(btn => {
            if (!btn.id.includes('auto')) {
                btn.disabled = true;
            }
        });
        showNotification('已切换到自动控制模式', 'info');
    } else if (mode === 'schedule') {
        // 定时模式
        showNotification('已切换到定时控制模式', 'info');
    }
}

// 应用风量变化
function applyAirflowChange(slider) {
    const value = slider.value;
    const zoneId = slider.id.replace('airflowSlider', '');
    
    console.log(`应用区域${zoneId}风量变化: ${value}%`);
    showNotification(`区域${zoneId}风量已调整至${value}%`, 'success');
}

// 调整风量
function adjustAirflow(zone, delta) {
    const slider = document.getElementById(`airflowSlider${zone}`);
    if (slider) {
        const currentValue = parseInt(slider.value);
        const newValue = Math.max(0, Math.min(100, currentValue + delta));
        slider.value = newValue;
        updateSliderValue(slider);
        applyAirflowChange(slider);
    }
}

// 启用自动控制
function enableAutoControl(zone) {
    console.log(`启用区域${zone}自动控制`);
    showNotification(`区域${zone}已启用自动控制`, 'success');
    
    // 模拟自动调节
    const slider = document.getElementById(`airflowSlider${zone}`);
    if (slider) {
        const targetValue = 85; // 目标值
        const currentValue = parseInt(slider.value);
        
        // 逐步调节到目标值
        const step = currentValue < targetValue ? 1 : -1;
        const interval = setInterval(() => {
            const newValue = parseInt(slider.value) + step;
            if ((step > 0 && newValue >= targetValue) || (step < 0 && newValue <= targetValue)) {
                slider.value = targetValue;
                updateSliderValue(slider);
                clearInterval(interval);
                showNotification(`区域${zone}已自动调节到最优值`, 'success');
            } else {
                slider.value = newValue;
                updateSliderValue(slider);
            }
        }, 200);
    }
}

// 计算ACH
function calculateACH() {
    const roomVolume = parseFloat(document.getElementById('roomVolume')?.value) || 200;
    const cleanClass = document.getElementById('cleanClass')?.value || '5';
    const personCount = parseInt(document.getElementById('personCount')?.value) || 4;
    const heatLoad = parseFloat(document.getElementById('heatLoad')?.value) || 5.2;
    
    // 根据洁净等级推荐ACH
    const achRecommendations = {
        '5': { min: 15, max: 20 },
        '6': { min: 12, max: 18 },
        '7': { min: 10, max: 15 },
        '8': { min: 8, max: 12 }
    };
    
    const recommendation = achRecommendations[cleanClass];
    const minAirflow = (recommendation.min * roomVolume).toFixed(0);
    const recommendedAirflow = (recommendation.max * roomVolume).toFixed(0);
    
    // 更新结果显示
    const resultElements = document.querySelectorAll('.result-value');
    if (resultElements.length >= 4) {
        resultElements[0].innerHTML = `${recommendation.min}-${recommendation.max} <span class="unit">次/h</span>`;
        resultElements[1].innerHTML = `${minAirflow} <span class="unit">m³/h</span>`;
        resultElements[2].innerHTML = `${recommendedAirflow} <span class="unit">m³/h</span>`;
        resultElements[3].innerHTML = `85% <span class="unit">达标</span>`;
    }
    
    showNotification('ACH计算完成', 'success');
}

// 选择气流模式
function selectAirflowPattern(btn) {
    // 移除其他按钮的active状态
    document.querySelectorAll('.pattern-btn').forEach(b => b.classList.remove('active'));
    
    // 添加当前按钮的active状态
    btn.classList.add('active');
    
    const pattern = btn.getAttribute('data-pattern');
    console.log('选择气流模式:', pattern);
    
    // 更新气流预览
    updateAirflowPreview(pattern);
    
    showNotification(`已切换到${btn.textContent}模式`, 'info');
}

// 更新气流预览
function updateAirflowPreview(pattern) {
    const flowLines = document.querySelectorAll('.flow-line');
    const outlets = document.querySelectorAll('.outlet');
    
    // 根据模式更新动画效果
    switch (pattern) {
        case 'laminar':
            // 层流：直线流动
            flowLines.forEach((line, index) => {
                line.style.animation = `laminarFlow 2s infinite linear`;
                line.style.animationDelay = `${index * 0.2}s`;
            });
            break;
        case 'turbulent':
            // 湍流：波动流动
            flowLines.forEach((line, index) => {
                line.style.animation = `turbulentFlow 1.5s infinite ease-in-out`;
                line.style.animationDelay = `${index * 0.1}s`;
            });
            break;
        case 'mixed':
            // 混合：组合流动
            flowLines.forEach((line, index) => {
                line.style.animation = `mixedFlow 2.5s infinite ease-in-out`;
                line.style.animationDelay = `${index * 0.15}s`;
            });
            break;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 
                           type === 'warning' ? 'exclamation-triangle' : 
                           type === 'error' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    const colors = {
        success: 'var(--success-color)',
        warning: 'var(--warning-color)',
        error: 'var(--danger-color)',
        info: 'var(--info-color)'
    };
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type]};
        color: white;
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// 启动实时数据更新
function startRealTimeUpdates() {
    // 每10秒更新一次风量数据
    setInterval(() => {
        updateAirflowMetrics();
    }, 10000);
}

// 处理数据刷新事件
function handleDataRefresh(event) {
    console.log('刷新风量控制数据', event.detail);
    updateAirflowMetrics();
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    @keyframes laminarFlow {
        0% { opacity: 0.3; transform: translateX(0); }
        100% { opacity: 1; transform: translateX(20px); }
    }
    
    @keyframes turbulentFlow {
        0%, 100% { opacity: 0.5; transform: translateY(0); }
        50% { opacity: 1; transform: translateY(-5px); }
    }
    
    @keyframes mixedFlow {
        0% { opacity: 0.3; transform: translate(0, 0); }
        50% { opacity: 1; transform: translate(10px, -3px); }
        100% { opacity: 0.3; transform: translate(20px, 0); }
    }
`;
document.head.appendChild(style);
