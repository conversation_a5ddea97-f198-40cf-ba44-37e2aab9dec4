<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洁净空调系统 - AI模型</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e40af'%3E%3Cpath d='M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z'/%3E%3C/svg%3E">
</head>
<body>
    <!-- 导航栏 -->
    <div id="navigation-container"></div>
    
    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 页面头部 -->
        <div id="header-container"></div>
        
        <!-- AI模型内容 -->
        <div class="ai-models-container">
            <div class="container">
                <div class="grid grid-cols-2 gap-6">
                    <!-- CFD模拟 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-wind"></i>
                                CFD空气流场模拟
                            </h3>
                        </div>
                        <div class="cfd-simulation">
                            <div class="simulation-preview">
                                <div class="flow-field">
                                    <div class="velocity-vectors">
                                        <div class="vector" style="top: 20%; left: 10%;"></div>
                                        <div class="vector" style="top: 30%; left: 30%;"></div>
                                        <div class="vector" style="top: 40%; left: 50%;"></div>
                                        <div class="vector" style="top: 50%; left: 70%;"></div>
                                        <div class="vector" style="top: 60%; left: 90%;"></div>
                                    </div>
                                    <div class="pressure-contours">
                                        <div class="contour high"></div>
                                        <div class="contour medium"></div>
                                        <div class="contour low"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="simulation-controls">
                                <div class="control-group">
                                    <label class="form-label">模拟类型</label>
                                    <select class="form-select">
                                        <option>稳态流场</option>
                                        <option>瞬态流场</option>
                                        <option>颗粒轨迹</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="form-label">边界条件</label>
                                    <select class="form-select">
                                        <option>标准工况</option>
                                        <option>高负荷工况</option>
                                        <option>节能工况</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary">开始模拟</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预测性维护 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-brain"></i>
                                AI预测性维护
                            </h3>
                        </div>
                        <div class="predictive-maintenance">
                            <div class="prediction-item">
                                <div class="prediction-header">
                                    <div class="device-name">FFU-001</div>
                                    <div class="risk-level high">高风险</div>
                                </div>
                                <div class="prediction-details">
                                    <div class="prediction-type">过滤器堵塞预警</div>
                                    <div class="prediction-time">预计7天后需要更换</div>
                                    <div class="confidence">置信度: 92%</div>
                                </div>
                                <div class="prediction-actions">
                                    <button class="btn btn-sm btn-warning">安排维护</button>
                                </div>
                            </div>
                            <div class="prediction-item">
                                <div class="prediction-header">
                                    <div class="device-name">FFU-003</div>
                                    <div class="risk-level medium">中风险</div>
                                </div>
                                <div class="prediction-details">
                                    <div class="prediction-type">风机轴承磨损</div>
                                    <div class="prediction-time">预计30天后需要保养</div>
                                    <div class="confidence">置信度: 78%</div>
                                </div>
                                <div class="prediction-actions">
                                    <button class="btn btn-sm btn-secondary">查看详情</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 模型训练与管理 -->
                <div class="card mt-6">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs"></i>
                            模型训练与管理
                        </h3>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            新建模型
                        </button>
                    </div>
                    <div class="models-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>模型名称</th>
                                    <th>类型</th>
                                    <th>训练状态</th>
                                    <th>准确率</th>
                                    <th>最后训练</th>
                                    <th>版本</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>过滤器寿命预测模型</td>
                                    <td>回归模型</td>
                                    <td><span class="status-indicator status-success">已部署</span></td>
                                    <td>92.5%</td>
                                    <td>2024-12-15</td>
                                    <td>v2.1</td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看</button>
                                        <button class="btn btn-sm btn-primary">重训练</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>设备故障检测模型</td>
                                    <td>分类模型</td>
                                    <td><span class="status-indicator status-warning">训练中</span></td>
                                    <td>87.3%</td>
                                    <td>2024-12-18</td>
                                    <td>v1.5</td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看</button>
                                        <button class="btn btn-sm btn-secondary">停止</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>能耗优化模型</td>
                                    <td>强化学习</td>
                                    <td><span class="status-indicator status-success">已部署</span></td>
                                    <td>89.1%</td>
                                    <td>2024-12-10</td>
                                    <td>v1.2</td>
                                    <td>
                                        <button class="btn btn-sm btn-secondary">查看</button>
                                        <button class="btn btn-sm btn-primary">重训练</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <style>
        .ai-models-container {
            padding: var(--spacing-lg);
        }
        
        .cfd-simulation {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .simulation-preview {
            height: 200px;
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
        }
        
        .flow-field {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .velocity-vectors {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        
        .vector {
            position: absolute;
            width: 20px;
            height: 2px;
            background: var(--primary-color);
            transform-origin: left center;
            animation: flow 2s infinite linear;
        }
        
        .vector::after {
            content: '';
            position: absolute;
            right: -4px;
            top: -2px;
            width: 0;
            height: 0;
            border-left: 6px solid var(--primary-color);
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
        }
        
        @keyframes flow {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }
        
        .pressure-contours {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.3;
        }
        
        .contour {
            position: absolute;
            border-radius: 50%;
        }
        
        .contour.high {
            top: 20%;
            left: 20%;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.4), transparent);
        }
        
        .contour.medium {
            top: 40%;
            left: 50%;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(245, 158, 11, 0.4), transparent);
        }
        
        .contour.low {
            top: 60%;
            left: 10%;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.4), transparent);
        }
        
        .simulation-controls {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .predictive-maintenance {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .prediction-item {
            padding: var(--spacing-md);
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .prediction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-sm);
        }
        
        .device-name {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .risk-level {
            font-size: 0.75rem;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .risk-level.high {
            background: rgba(220, 38, 38, 0.1);
            color: var(--danger-color);
        }
        
        .risk-level.medium {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .risk-level.low {
            background: rgba(5, 150, 105, 0.1);
            color: var(--success-color);
        }
        
        .prediction-details {
            margin-bottom: var(--spacing-md);
        }
        
        .prediction-type {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .prediction-time {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xs);
        }
        
        .confidence {
            font-size: 0.75rem;
            color: var(--text-muted);
            font-family: var(--font-mono);
        }
        
        .models-table {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }
        
        .data-table th,
        .data-table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .data-table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }
        
        .data-table td {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .data-table tr:hover {
            background: var(--bg-hover);
        }
    </style>
    
    <!-- 脚本文件 -->
    <script src="assets/js/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const pageTitle = document.getElementById('pageTitle');
                const breadcrumb = document.getElementById('breadcrumb');
                
                if (pageTitle) {
                    pageTitle.textContent = 'AI模型';
                }
                
                if (breadcrumb) {
                    breadcrumb.innerHTML = `
                        <span class="breadcrumb-item">首页</span>
                        <i class="fas fa-chevron-right breadcrumb-separator"></i>
                        <span class="breadcrumb-item active">AI模型</span>
                    `;
                }
            }, 500);
        });
    </script>
</body>
</html>
