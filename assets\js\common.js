// 洁净空调系统 - 公共JavaScript文件

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载导航栏和头部组件
    loadComponents();
    
    // 初始化公共功能
    initCommonFeatures();
});

// 加载组件
async function loadComponents() {
    try {
        // 加载导航栏
        const navigationResponse = await fetch('components/navigation.html');
        const navigationHTML = await navigationResponse.text();
        const navigationContainer = document.getElementById('navigation-container');
        if (navigationContainer) {
            navigationContainer.innerHTML = navigationHTML;
        }



        // 加载页脚
        const footerResponse = await fetch('components/footer.html');
        const footerHTML = await footerResponse.text();
        const footerContainer = document.getElementById('footer-container');
        if (footerContainer) {
            footerContainer.innerHTML = footerHTML;
        }

        // 组件加载完成后执行脚本
        executeComponentScripts();

    } catch (error) {
        console.error('加载组件失败:', error);
    }
}

// 执行组件中的脚本
function executeComponentScripts() {
    // 执行导航栏脚本
    const navigationScripts = document.querySelectorAll('#navigation-container script');
    navigationScripts.forEach(script => {
        const newScript = document.createElement('script');
        newScript.textContent = script.textContent;
        document.body.appendChild(newScript);
    });
    
    // 执行头部脚本
    const headerScripts = document.querySelectorAll('#header-container script');
    headerScripts.forEach(script => {
        const newScript = document.createElement('script');
        newScript.textContent = script.textContent;
        document.body.appendChild(newScript);
    });
}

// 初始化公共功能
function initCommonFeatures() {
    // 初始化工具提示
    initTooltips();
    
    // 初始化模态框
    initModals();
    
    // 初始化表单验证
    initFormValidation();
    
    // 初始化数据刷新
    initDataRefresh();
}

// 工具提示功能
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const element = event.target;
    const title = element.getAttribute('title');
    if (!title) return;
    
    // 创建工具提示元素
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = title;
    tooltip.style.cssText = `
        position: absolute;
        background: var(--bg-tertiary);
        color: var(--text-primary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        white-space: nowrap;
        z-index: 10000;
        pointer-events: none;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
    `;
    
    document.body.appendChild(tooltip);
    
    // 定位工具提示
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    
    tooltip.style.left = rect.left + (rect.width - tooltipRect.width) / 2 + 'px';
    tooltip.style.top = rect.top - tooltipRect.height - 8 + 'px';
    
    // 存储引用以便清理
    element._tooltip = tooltip;
    
    // 临时移除title属性
    element._originalTitle = title;
    element.removeAttribute('title');
}

function hideTooltip(event) {
    const element = event.target;
    if (element._tooltip) {
        document.body.removeChild(element._tooltip);
        delete element._tooltip;
    }
    
    // 恢复title属性
    if (element._originalTitle) {
        element.setAttribute('title', element._originalTitle);
        delete element._originalTitle;
    }
}

// 模态框功能
function initModals() {
    // 关闭模态框的通用处理
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal-overlay')) {
            closeModal(event.target.closest('.modal'));
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modal) {
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// 表单验证
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate]');
    forms.forEach(form => {
        form.addEventListener('submit', validateForm);
    });
}

function validateForm(event) {
    const form = event.target;
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, '此字段为必填项');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    if (!isValid) {
        event.preventDefault();
    }
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.cssText = `
        color: var(--danger-color);
        font-size: 0.75rem;
        margin-top: var(--spacing-xs);
    `;
    
    field.parentNode.appendChild(errorElement);
    field.classList.add('error');
}

function clearFieldError(field) {
    const errorElement = field.parentNode.querySelector('.field-error');
    if (errorElement) {
        errorElement.remove();
    }
    field.classList.remove('error');
}

// 数据刷新功能
function initDataRefresh() {
    // 自动刷新间隔（毫秒）
    const AUTO_REFRESH_INTERVAL = 30000; // 30秒
    
    // 设置自动刷新
    setInterval(() => {
        refreshData();
    }, AUTO_REFRESH_INTERVAL);
}

function refreshData() {
    // 触发自定义事件，让各个页面监听并处理数据刷新
    const refreshEvent = new CustomEvent('dataRefresh', {
        detail: { timestamp: Date.now() }
    });
    document.dispatchEvent(refreshEvent);
}

// 工具函数
const Utils = {
    // 格式化数字
    formatNumber: function(num, decimals = 1) {
        if (num === null || num === undefined) return '--';
        return Number(num).toFixed(decimals);
    },
    
    // 格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    // 格式化相对时间
    formatRelativeTime: function(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        return `${days}天前`;
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 生成随机ID
    generateId: function() {
        return 'id_' + Math.random().toString(36).substr(2, 9);
    },
    
    // 深拷贝对象
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    // 本地存储封装
    storage: {
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('存储数据失败:', e);
            }
        },
        
        get: function(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('读取数据失败:', e);
                return defaultValue;
            }
        },
        
        remove: function(key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('删除数据失败:', e);
            }
        }
    }
};

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    // 这里可以添加错误上报逻辑
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
    // 这里可以添加错误上报逻辑
});

// 导出工具函数供其他脚本使用
window.Utils = Utils;
window.showModal = showModal;
window.closeModal = closeModal;
